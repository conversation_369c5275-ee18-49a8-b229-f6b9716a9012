<script setup lang="ts">
import { Card } from '@ownego/polaris-vue';
import { storeToRefs } from 'pinia';
import { onMounted, watch, ref } from 'vue';

import { useFormsStore } from '@/stores/formsStore';

import {
    resourceName,
    FormStatusOptions as FormDataStatusOptions,
    formTableHeadings as FormTableHeadings,
} from '../configs';
import { ListBlock } from './list-block';
import { EmptyStateSnippet } from './list-block/empty-state-snippet';
import { PaginateBlock } from './paginate-block';
import { SkeletonBlock } from './skeleton-block';
import { useFilters } from './utils';

const formsStore = useFormsStore();
const {
    forms,
    isLoading,
    paginatedForms,
    filteredForms,
    currentPage,
    totalPages,
    startItem,
    endItem,
    selectedFilterIndex,
    status: storeStatus,
} = storeToRefs(formsStore);

const listBlockRef = ref();

const { status, handleStatus, appliedFilters, handleQueryValueRemove, handleFiltersClearAll } = useFilters();

watch(status, (newStatus) => {
    if (JSON.stringify(newStatus) !== JSON.stringify(storeStatus.value)) {
        formsStore.setStatus(newStatus);
    }
});

watch(storeStatus, (newStatus) => {
    if (JSON.stringify(newStatus) !== JSON.stringify(status.value)) {
        handleStatus(newStatus);
    }
});

onMounted(() => {
    formsStore.fetchForms();
});

const handleFiltersSelect = (index: number) => {
    formsStore.setSelectedFilterIndex(index);
};

const handleFiltersQueryChange = (value: string) => {
    formsStore.setQueryValue(value);
};

const updateStatus = (ids: string[], newStatus: string) => {
    formsStore.updateStatus(ids, newStatus);
};

const handleQueryClear = () => {
    handleQueryValueRemove();
    formsStore.clearQueryValue();
};

const handleClearAllFilters = () => {
    handleFiltersClearAll();
    formsStore.clearAllFilters();
};

watch(paginatedForms, () => {
    listBlockRef.value?.clearSelection();
});
</script>

<template>
    <SkeletonBlock v-if="isLoading" />
    <template v-else>
        <Card v-if="forms.length > 0" padding="none">
            <ListBlock
                ref="listBlockRef"
                :status="status"
                :appliedFilters="appliedFilters"
                :form-data="paginatedForms"
                :resourceName="resourceName"
                :tableHeadings="FormTableHeadings"
                :FormDataStatusOptions="FormDataStatusOptions"
                :selected="selectedFilterIndex"
                :handleFiltersSelect="handleFiltersSelect"
                :update-status="updateStatus"
                :handleFiltersQueryChange="handleFiltersQueryChange"
                :handleStatus="handleStatus"
                :handleQueryValueRemove="handleQueryClear"
                :handleFiltersClearAll="handleClearAllFilters"
            />
            <PaginateBlock
                :currentPage="currentPage"
                :totalPages="totalPages"
                :totalItems="filteredForms.length"
                :startItem="startItem"
                :endItem="endItem"
                :goToPreviousPage="formsStore.goToPreviousPage"
                :goToNextPage="formsStore.goToNextPage"
            />
        </Card>

        <Card v-else padding="none">
            <EmptyStateSnippet />
        </Card>
    </template>
</template>

<style scoped lang="scss"></style>
