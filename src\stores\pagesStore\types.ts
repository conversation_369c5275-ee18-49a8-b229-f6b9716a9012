export interface ChartItem {
    date: string;
    value: number;
}
export interface PageItem {
    id: string;
    title: string;
    status: string;
    type: string;
    url: string;
    updated_at: string;
    created_at: string;
    analyticId?: string;
}

export interface PagesStoreState {
    pages: PageItem[];
    isLoading: boolean;
    queryValue: string;
    status: string[];
    currentPage: number;
    itemsPerPage: number;
    activeTab: string;
}
