<script lang="ts" setup>
import { Box, Button, Card, Collapsible, InlineGrid, InlineStack } from '@ownego/polaris-vue';
import ChevronDownIcon from '@shopify/polaris-icons/ChevronDownIcon.svg';
import ChevronUpIcon from '@shopify/polaris-icons/ChevronUpIcon.svg';
import { ref, onMounted, computed, watch } from 'vue';

import type { ChartItem } from '@/views/analytic-view/analytic-detail-section/chart-block/types';

import { useAnalyticStore } from '@/stores/analyticStore';

import { ChartItemSnippet } from './chart-item-snippet';
import { ChartSnippet } from './chart-snippet';

const analyticStore = useAnalyticStore();
const items = computed(() => analyticStore.items);

const activeIndex = computed<number | null>(() => {
    if (!analyticStore.activeDataKey) return null;
    const index = items.value.findIndex((item: ChartItem) => item.key === analyticStore.activeDataKey);
    return index >= 0 ? index : null;
});

const activeItem = computed<ChartItem | null>(() =>
    activeIndex.value !== null ? items.value[activeIndex.value] : null,
);

const isOpen = ref(true);

onMounted(async () => {
    await analyticStore.fetchAnalyticData(analyticStore.currentPeriod);
    if (items.value.length > 0) {
        analyticStore.setActiveDataKey(items.value[0].key);
    }
});

watch(
    activeItem,
    (newItem) => {
        if (newItem) {
            analyticStore.setActiveDataKey(newItem.key);
        }
    },
    { immediate: true },
);

const handleOpen = () => {
    isOpen.value = !isOpen.value;

    if (isOpen.value && activeIndex.value === null && items.value.length > 0) {
        analyticStore.setActiveDataKey(items.value[0].key);
    }
};

const toggleActive = (index: number) => {
    if (activeIndex.value === index) return;

    // Cập nhật activeDataKey để thay đổi dữ liệu biểu đồ theo item được chọn
    analyticStore.setActiveDataKey(items.value[index].key);
};
</script>

<template>
    <Card>
        <div class="chart-section-content">
            <InlineStack :align="'start'" :gap="200">
                <InlineGrid :columns="6" :gap="300" style="flex: 1">
                    <div
                        v-for="(item, index) in items"
                        :key="index"
                        class="chart-item-block-container"
                        :class="{ active: activeIndex === index }"
                        @click="toggleActive(index)"
                    >
                        <ChartItemSnippet :item="item" />
                    </div>
                </InlineGrid>

                <div class="icon-wrapper">
                    <Button class="icon-button" :icon="isOpen ? ChevronUpIcon : ChevronDownIcon" @click="handleOpen" />
                </div>
            </InlineStack>
            <Collapsible
                :open="isOpen"
                :transition="{ duration: 'fast', timingFunction: 'ease-in-out', delay: '500ms' }"
            >
                <Box :padding-block-end="400">
                    <ChartSnippet />
                </Box>
            </Collapsible>
        </div>
    </Card>
</template>

<style scoped>
.chart-section-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.chart-item-block-container {
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.chart-item-block-container.active {
    background-color: var(--m-gray-base-30);
}

.icon-wrapper {
    width: 24px;
}

.icon-button {
    box-shadow: none;
}
</style>
