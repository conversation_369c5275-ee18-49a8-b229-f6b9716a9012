import { pathnames } from '@configs/pathnames';
import { DashboardLayout } from '@layouts/dashboard-layout';
import { DashboardView } from '@views/dashboard-view';
import { createRouter, createWebHistory } from 'vue-router';

import { AnalyticView } from '@/views/analytic-view';
import { AnalyticDetailView } from '@/views/analytic-view/analytic-detail-section';
import { FeedbackView } from '@/views/feedback-view';
import { FormsView } from '@/views/forms-view';
import { SubmissionsDetailSection } from '@/views/forms-view/submissions-detail-section';
import { SubmissionsManagerSection } from '@/views/forms-view/submissions-manager-section';
import { PagesView } from '@/views/pages-view';
import { SettingsView } from '@/views/settings-view';
import { TestingView } from '@/views/testing-view';

const router = createRouter({
    history: createWebHistory(import.meta.env.BASE_URL),
    routes: [
        {
            path: pathnames.dashboard,
            component: DashboardLayout,
            children: [
                {
                    path: '',
                    name: 'dashboard',
                    component: DashboardView,
                },
                {
                    path: pathnames.pages,
                    name: 'pages',
                    // route level code-splitting
                    // this generates a separate chunk (About.[hash].js) for this route
                    // which is lazy-loaded when the route is visited.
                    // component: () => import('@views/pages-view'),
                    component: PagesView,
                },
                {
                    path: 'feedback',
                    name: 'feedback',
                    component: FeedbackView,
                },
                {
                    path: 'forms',
                    name: 'forms',
                    component: FormsView,
                },
                {
                    path: 'submissions/:id',
                    name: 'submissions',
                    component: SubmissionsManagerSection,
                },
                {
                    path: 'submissions/:id/detail',
                    name: 'submissions-detail',
                    component: SubmissionsDetailSection,
                },
                {
                    path: 'settings',
                    name: 'settings',
                    component: SettingsView,
                },
                {
                    path: 'analytic/detail/:id',
                    name: 'analytics',
                    component: AnalyticDetailView,
                },
                {
                    path: 'analytics',
                    name: 'analytic',
                    component: AnalyticView,
                },
                {
                    path: 'ab-testing',
                    name: 'testing',
                    component: TestingView,
                },
            ],
        },
    ],
});

export default router;
