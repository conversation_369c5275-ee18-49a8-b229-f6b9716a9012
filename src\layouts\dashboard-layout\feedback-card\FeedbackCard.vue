<script setup lang="ts">
import { feedbackLocaleVars } from '@locales/feedback';
import { Card, BlockStack, InlineStack, Text, ButtonGroup, Button, Page } from '@ownego/polaris-vue';
import ThumbsDownIcon from '@shopify/polaris-icons/ThumbsDownIcon.svg';
import ThumbsUpIcon from '@shopify/polaris-icons/ThumbsUpIcon.svg';
import XIcon from '@shopify/polaris-icons/XIcon.svg';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter, useRoute } from 'vue-router';

const { t } = useI18n();
const router = useRouter();
const route = useRoute();
const feedbackCardRef = ref<HTMLElement>();

const handleGood = async () => {
    if (route.path === '/feedback' || route.name === 'feedback') {
        if (feedbackCardRef.value) {
            feedbackCardRef.value.classList.add('hidden');
        }
    } else {
        await router.push(`/feedback`);
    }
};
const handleBad = () => {
    if (feedbackCardRef.value) {
        feedbackCardRef.value.classList.add('hidden');
    }
};
const handleClose = () => {
    if (feedbackCardRef.value) {
        feedbackCardRef.value.classList.add('hidden');
    }
};
</script>
<template>
    <Page>
        <div class="feedback-card" ref="feedbackCardRef">
            <Card>
                <BlockStack :gap="'400'" :align="'start'">
                    <BlockStack :gap="'200'">
                        <InlineStack :align="'space-between'">
                            <Text as="h2" variant="headingMd">{{ t(feedbackLocaleVars.shareYourFeedback) }}</Text>
                            <XIcon width="1rem" height="1rem" style="cursor: pointer" @click="handleClose" />
                        </InlineStack>

                        <Text as="p" variant="bodyMd" tone="subdued">
                            {{ t(feedbackLocaleVars.experienceDescription) }}
                        </Text>
                    </BlockStack>
                    <ButtonGroup>
                        <Button :icon="ThumbsUpIcon" @click="handleGood">{{ t(feedbackLocaleVars.good) }}</Button>

                        <Button :icon="ThumbsDownIcon" @click="handleBad">{{ t(feedbackLocaleVars.bad) }}</Button>
                    </ButtonGroup>
                </BlockStack>
            </Card>
        </div>
    </Page>
</template>
<style scoped lang="scss">
.hidden {
    display: none;
}

.feedback-card-container {
    height: 120px;
}
</style>
