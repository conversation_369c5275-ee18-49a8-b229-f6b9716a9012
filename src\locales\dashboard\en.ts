import vars from './vars';

export const dashboardLocaleEN = {
    [vars.startBuilding]: 'Start building',
    [vars.welcomeTitle]: 'Welcome to MateStore Page Builder, easy-shop 👋',
    [vars.welcomeDescription]:
        'Lorem ipsum dolor, sit amet consectetur adipisicing elit. <PERSON><PERSON><PERSON>, inventore.Pariatur, inventore.Pariatur, inventore. Pariatur, inventore.Pariatur, inventore.Pariatur, inventore.Pariatur, inventore.Pariatur, inventore.Pariatur, inventore.',

    [vars.cardCreatePageTitle]: 'Create new page',
    [vars.cardCreateSectionTitle]: 'Create new section',
    [vars.cardCreateBlockTitle]: 'Create new block',

    [vars.cardCreatePageDesc]: 'Lorem ipsum dolor sit amet consectetur adipisicing elit. Illo, nihil!',
    [vars.cardCreateSectionDesc]: 'Lorem ipsum dolor sit amet consectetur adipisicing elit. Illo, nihil!',
    [vars.cardCreateBlockDesc]: 'Lorem ipsum dolor sit amet consectetur adipisicing elit. Illo, nihil!',

    [vars.bannerHeading]: 'Enable MateStore - Landing Page Builder in your theme',
    [vars.bannerDescription]:
        'The form will be displayed correctly on live page when the MateStore - Landing Page Builder Theme Helper is enabled in your theme.',
    [vars.bannerLearnMore]: 'Learn more about Theme App Extension',
    [vars.bannerButtonText]: 'Turn on Shopify Editor',

    [vars.sliderTitle]: "What's new?",
    [vars.sliderSeeAll]: 'See all news items',

    [vars.sliderItemTitle]: 'Create new page',
    [vars.sliderItemDescription]: 'Lorem ipsum dolor sit amet consectetur adipisicing elit. Illo, nihil!',
    [vars.sliderItemButtonText]: 'Explore now',
    [vars.sliderItemDismiss]: 'Dismiss',

    [vars.setupGuideShowButton]: 'Show Setup Guide',

    [vars.setupGuideTitle]: 'Setup Guide',
    [vars.setupGuideDescription]: 'Use this personalized guide to get your app up and running.',
    [vars.setupGuideDone]: 'Done',
    [vars.setupGuideCompleted]: 'completed',
    [vars.setupGuideDismiss]: 'Dismiss',
    [vars.setupGuideDismissButton]: 'Dismiss Guide',

    [vars.setupItemAddProduct]: 'Add your first product',
    [vars.setupItemAddProductDesc]:
        'If checking out takes longer than 30 seconds, half of all shoppers quit. Let your customers check out quickly with a one-step payment solution.',
    [vars.setupItemAddProductBtn]: 'Add product',
    [vars.setupItemImportProductsBtn]: 'Import products',
    [vars.setupItemShareStore]: 'Share your online store',
    [vars.setupItemShareStoreDesc]:
        'Drive awareness and traffic by sharing your store via SMS and email with your closest network, and on communities like Instagram, TikTok, Facebook, and Reddit.',
    [vars.setupItemCopyLinkBtn]: 'Copy store link',
    [vars.setupItemTranslate]: 'Translate your store',
    [vars.setupItemTranslateDesc]:
        'Translating your store improves cross-border conversion by an average of 13%. Add languages for your top customer regions for localized browsing, notifications, and checkout.',
    [vars.setupItemAddLanguageBtn]: 'Add a language',

    [vars.supportSectionTitle]: 'Support',

    [vars.supportLiveChatTitle]: 'Live chat / Email',
    [vars.supportLiveChatDesc]: '24/7 live chat support, reply and assist instantly.',
    [vars.supportLiveChatBtn]: 'Chat now',
    [vars.supportEmailBtn]: 'Send email',
    [vars.supportHelpCenterTitle]: 'Help center',
    [vars.supportHelpCenterDesc]: 'Find answers to your questions in our help center',
    [vars.supportHelpCenterBtn]: 'Visit now',
    [vars.supportCommunityTitle]: 'Community',
    [vars.supportCommunityDesc]: 'Connect, share ideas, and find inspiration!',
    [vars.supportCommunityBtn]: 'Join now',
    [vars.supportFeatureRequestTitle]: 'Request new features',
    [vars.supportFeatureRequestDesc]: "Can't find the feature you need? Request a new one!",
    [vars.supportFeatureRequestBtn]: 'Make request',

    [vars.miniMenuProfile]: 'Account',
    [vars.miniMenuNotifications]: 'Notifications',
    [vars.miniMenuStore]: 'Plans',
    [vars.miniMenuHelp]: 'Help center',
    [vars.miniMenuLanguage]: 'Language',
    [vars.miniMenuCreateNewPage]: 'Create new page',

    [vars.templatesTitle]: 'Templates',
    [vars.nextButton]: 'Next',
    [vars.searchTemplatesPlaceholder]: 'Search templates...',
    [vars.noTemplatesFound]: 'No templates found',
    [vars.usedTimes]: 'Used {count} times',
    [vars.selected]: 'Selected',
    [vars.select]: 'Select',

    [vars.tabAll]: 'All',
    [vars.tabRegular]: 'Regular',
    [vars.tabHome]: 'Home',
    [vars.tabProduct]: 'Product',
    [vars.tabCollection]: 'Collection',
    [vars.tabListCollection]: 'List Collection',
};
