<script setup lang="ts">
import { Badge, Text, Box, Card, Page } from '@ownego/polaris-vue';
import { computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { SubmissionDatas } from '../configs';

const route = useRoute();
const router = useRouter();

const id = route.params.id as string;

const submission = computed(() => SubmissionDatas.find((s) => s.id === id));
</script>

<template>
    <Page
        :title="submission?.form_id"
        :subtitle="submission?.gmail"
        compactTitle
        :backAction="{
            content: 'Products',
            onAction: () => {
                router.push('/submissions/' + submission?.form_id);
            },
        }"
    >
        <template #pageTitle>
            <span class="badge-container">
                <Badge
                    class="status-badge"
                    :class="`status-badge--${submission?.status.toLowerCase()}`"
                    :progress="submission?.status === 'Read' ? 'complete' : 'incomplete'"
                >
                    {{ submission?.status }}
                </Badge>
            </span>
        </template>
        <Card padding="none">
            <Box :padding="300">
                <Text as="h1" variant="heading2xl" font-weight="bold"> Submissions Detail </Text>
            </Box>

            <div v-if="submission" class="p-4 space-y-3">
                <div class="detail-row">
                    <strong>ID:</strong> <span>{{ submission.id }}</span>
                </div>
                <div class="detail-row">
                    <strong>Form ID:</strong> <span>{{ submission.form_id }}</span>
                </div>
                <div class="detail-row">
                    <strong>Email:</strong> <span>{{ submission.gmail }}</span>
                </div>
                <div class="detail-row">
                    <strong>First Name:</strong> <span>{{ submission.first_name }}</span>
                </div>
                <div class="detail-row">
                    <strong>Last Name:</strong> <span>{{ submission.last_name }}</span>
                </div>
                <div class="detail-row" style="width: 190px">
                    <strong>Status:</strong>
                    <span class="badge-container">
                        <Badge
                            class="status-badge"
                            :class="`status-badge--${submission.status.toLowerCase()}`"
                            :progress="submission.status === 'Read' ? 'complete' : 'incomplete'"
                        >
                            {{ submission.status }}
                        </Badge>
                    </span>
                </div>
                <div class="detail-row">
                    <strong>Last Updated:</strong> <span>{{ submission.Last_updated }}</span>
                </div>
            </div>
            <div v-else class="p-4 text-gray-500">Submission not found</div>
        </Card>
    </Page>
</template>

<style scoped lang="scss">
.p-4 {
    padding: 1rem;
}

.space-y-3 > * + * {
    margin-top: 0.75rem;
}

.detail-row {
    display: flex;
    align-items: center;

    strong {
        min-width: 120px;
        margin-right: 8px;
    }

    span {
        flex: 1;
    }
}

.badge-container {
    display: inline-flex;
    align-items: center;
    width: 100px;
}

.status-badge {
    &--read {
        background-color: var(--m-blue-base-20);
        color: var(--m-success-base-30);

        svg {
            fill: var(--m-success-base-30);
        }
    }

    &--unread {
        background-color: var(--m-overlay-color-2);
    }
}
</style>
