<script setup lang="ts">
import { Box, InlineStack } from '@ownego/polaris-vue';

import { SearchSnippet } from './search-snippet';
import { TabSnippet } from './tab-snippet';

const props = defineProps<{
    activeTab: string;
}>();

const emit = defineEmits<{
    'tab-change': [tabId: string];
    'search-change': [query: string];
}>();

const handleTabChange = (tabId: string) => {
    emit('tab-change', tabId);
};

const handleSearchChange = (query: string) => {
    emit('search-change', query);
};
</script>
<template>
    <Box padding="400">
        <InlineStack :align="'space-between'">
            <TabSnippet :active-tab="props.activeTab" @tab-change="handleTabChange" />
            <SearchSnippet @search-change="handleSearchChange" />
        </InlineStack>
    </Box>
</template>
<style scoped lang="scss"></style>
