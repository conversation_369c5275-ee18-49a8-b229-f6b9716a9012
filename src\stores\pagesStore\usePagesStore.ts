import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

import type { PageItem } from './types';

export const usePagesStore = defineStore('pages', () => {
    const pages = ref<PageItem[]>([]);
    const isLoading = ref(false);
    const queryValue = ref('');
    const status = ref<string[]>([]);
    const type = ref<string[]>([]);
    const metric = ref<string[]>([]);
    const currentPage = ref(1);
    const itemsPerPage = ref(3);
    const activeTab = ref('All');

    const filteredPages = computed(() => {
        let filtered = [...pages.value];

        if (activeTab.value !== 'All') {
            filtered = filtered.filter((page) => page.type === activeTab.value);
        }

        if (status.value.length > 0) {
            if (status.value.includes('All') && status.value.length === 1) {
                return filtered;
            }
            filtered = filtered.filter((page) => status.value.includes(page.status));
        }

        if (queryValue.value) {
            filtered = filtered.filter((page) => page.title?.toLowerCase().includes(queryValue.value.toLowerCase()));
        }

        return filtered;
    });

    const paginatedPages = computed(() => {
        const start = (currentPage.value - 1) * itemsPerPage.value;
        const end = start + itemsPerPage.value;
        return filteredPages.value.slice(start, end);
    });

    const totalPages = computed(() => Math.max(1, Math.ceil(filteredPages.value.length / itemsPerPage.value)));

    const totalItems = computed(() => filteredPages.value.length);

    const startItem = computed(() => (currentPage.value - 1) * itemsPerPage.value + 1);

    const endItem = computed(() => Math.min(startItem.value + itemsPerPage.value - 1, totalItems.value));

    // actions
    function fetchPages() {
        isLoading.value = true;

        setTimeout(() => {
            pages.value = [
                {
                    id: '1020',
                    title: 'Regular page 1',
                    analyticId: '1020',
                    url: 'Not available',
                    status: 'Unpublished',
                    type: 'Landing',
                    updated_at: 'Nov 26, 2024',
                    created_at: 'Nov 26, 2024',
                },
                {
                    id: '1019',
                    title: 'Home page 1',
                    analyticId: '1019',
                    url: '/',
                    status: 'Published',
                    type: 'Home',
                    updated_at: 'Nov 26, 2024',
                    created_at: 'Nov 26, 2024',
                },
                {
                    id: '1018',
                    title: 'Product page 1',
                    analyticId: '1018',
                    url: '/product/1',
                    status: 'Published',
                    type: 'Product',
                    updated_at: 'Nov 26, 2024',
                    created_at: 'Nov 26, 2024',
                },
                {
                    id: '1021',
                    title: 'Regular page 3',
                    analyticId: '1021',
                    url: 'Not available',
                    status: 'Draft',
                    type: 'Landing',
                    updated_at: 'Nov 27, 2024',
                    created_at: 'Nov 27, 2024',
                },
                {
                    id: '1022',
                    title: 'Regular page 4',
                    analyticId: '1022',
                    url: 'Not available',
                    status: 'Unpublished',
                    type: 'Landing',
                    updated_at: 'Nov 27, 2024',
                    created_at: 'Nov 27, 2024',
                },
                {
                    id: '1023',
                    title: 'Product page 2',
                    analyticId: '1023',
                    url: '/product/2',
                    status: 'Draft',
                    type: 'Product',
                    updated_at: 'Nov 26, 2024',
                    created_at: 'Nov 26, 2024',
                },
                {
                    id: '1021',
                    title: 'Regular page 5',
                    analyticId: '1021',
                    url: 'Not available',
                    status: 'Unpublished',
                    type: 'Landing',
                    updated_at: 'Nov 27, 2024',
                    created_at: 'Nov 27, 2024',
                },
                {
                    id: '1021',
                    title: 'Regular page 6',
                    analyticId: '1021',
                    url: 'Not available',
                    status: 'Unpublished',
                    type: 'Landing',
                    updated_at: 'Nov 27, 2024',
                    created_at: 'Nov 27, 2024',
                },
            ];

            isLoading.value = false;
        }, 1000);
    }

    function getPageById(id: string): PageItem | undefined {
        return pages.value.find((page) => page.id === id);
    }

    function setActiveTab(tab: string) {
        activeTab.value = tab;
        currentPage.value = 1;
    }

    function setQueryValue(value: string) {
        queryValue.value = value;
        currentPage.value = 1;
    }

    function setStatus(newStatus: string[]) {
        status.value = newStatus;
        currentPage.value = 1;
    }

    function clearStatus() {
        status.value = [];
    }

    function clearQueryValue() {
        queryValue.value = '';
    }

    function clearAllFilters() {
        status.value = [];
        queryValue.value = '';
    }

    function setCurrentPage(page: number) {
        if (page >= 1 && page <= totalPages.value) {
            currentPage.value = page;
        }
    }

    function goToPreviousPage() {
        if (currentPage.value > 1) {
            currentPage.value--;
        }
    }

    function goToNextPage() {
        if (currentPage.value < totalPages.value) {
            currentPage.value++;
        }
    }

    function updatePageStatus(id: string, newStatus: string) {
        const pageIndex = pages.value.findIndex((page) => page.id === id);
        if (pageIndex !== -1) {
            pages.value[pageIndex] = {
                ...pages.value[pageIndex],
                status: newStatus,
            };
        }
    }

    function deletePage(id: string) {
        pages.value = pages.value.filter((page) => page.id !== id);
    }

    return {
        pages,
        isLoading,
        queryValue,
        status,
        currentPage,
        itemsPerPage,
        activeTab,

        filteredPages,
        paginatedPages,
        totalPages,
        totalItems,
        startItem,
        endItem,

        fetchPages,
        getPageById,
        setActiveTab,
        setQueryValue,
        setStatus,
        clearStatus,
        clearQueryValue,
        clearAllFilters,
        setCurrentPage,
        goToPreviousPage,
        goToNextPage,
        updatePageStatus,
        deletePage,
    };
});
