import vars from './vars';

export const formsLocaleVI = {
    [vars.forms]: 'Biểu mẫu',
    [vars.submissions]: 'G<PERSON>i',

    [vars.formsManagerTitle]: 'Quản lý biểu mẫu',
    [vars.formsStatusLabel]: 'Trạng thái',
    [vars.formsStatusAll]: 'Tất cả',
    [vars.formsStatusPublished]: 'Đã xuất bản',
    [vars.formsStatusUnpublished]: 'Chưa xuất bản',

    [vars.formsTableFormId]: 'ID biểu mẫu',
    [vars.formsTableFormTitle]: 'Tiêu đề biểu mẫu',
    [vars.formsTableStatus]: 'Trạng thái',
    [vars.formsTableSubmissions]: 'Lượt gửi',
    [vars.formsTableLastUpdated]: 'Cập nhật lần cuối',

    [vars.formsActionsPublish]: 'Xuất bản',
    [vars.formsActionsUnpublish]: 'Hủy xuất bản',
    [vars.formsActionsEdit]: 'Chỉnh sửa',
    [vars.formsActionsView]: 'Xem',
    [vars.formsActionsDelete]: 'Xóa',
    [vars.formsActionsDuplicate]: 'Nhân bản',

    [vars.formsEmptyTitle]: 'Không tìm thấy biểu mẫu nào',
    [vars.formsEmptyMessage]: 'Tạo biểu mẫu đầu tiên của bạn ',
    [vars.formsEmptyCreateNew]: 'Tạo biểu mẫu mới',

    [vars.formsSearchPlaceholder]: 'Tìm kiếm biểu mẫu',

    [vars.submissionsTitle]: 'Dữ liệu đã gửi',
    [vars.submissionsStatusLabel]: 'Trạng thái',
    [vars.submissionsStatusAll]: 'Tất cả',
    [vars.submissionsStatusRead]: 'Đã đọc',
    [vars.submissionsStatusUnread]: 'Chưa đọc',
    [vars.submissionsLearnMore]: 'Tìm hiểu thêm về',
    [vars.submissionsEmptyTitle]: 'Không tìm thấy dữ liệu nào',
    [vars.submissionsEmptyMessage]: 'form này chưa có dữ liệu',
    [vars.submissionsGoBack]: 'Quay lại',
    [vars.submissionsSearchingIn]: 'Tìm kiếm trong tất cả',
    [vars.submissionsSortAscending]: 'Tăng dần',
    [vars.submissionsSortDescending]: 'Giảm dần',

    [vars.submissionsTableFormId]: 'ID biểu mẫu',
    [vars.submissionsTableEmail]: 'Email',
    [vars.submissionsTableName]: 'Tên',
    [vars.submissionsTableStatus]: 'Trạng thái',
    [vars.submissionsTableLastUpdated]: 'Cập nhật lần cuối',

    [vars.paginationPrevious]: 'Trước',
    [vars.paginationNext]: 'Tiếp',
    [vars.paginationOf]: 'của',
};
