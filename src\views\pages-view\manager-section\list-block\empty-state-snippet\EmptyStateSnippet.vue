<script setup lang="ts">
import EmptyStateImg from '@assets/svgs/empty-state-img.svg';
import { Box, InlineStack, BlockStack, Button, Text } from '@ownego/polaris-vue';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

import { pagesLocaleVars } from '@/locales/pages';

import type { EmptyStateSnippetProps } from './types';

const { t } = useI18n();

const props = withDefaults(defineProps<EmptyStateSnippetProps>(), {
    title: '',
    message: '',
});

const translatedTitle = computed(() => props.title || t(pagesLocaleVars.pageEmptyTitle));
const translatedMessage = computed(() => props.message || t(pagesLocaleVars.pageEmptyMessage));
</script>

<template>
    <Box>
        <InlineStack :align="'center'">
            <div class="empty-state-img">
                <EmptyStateImg />
            </div>
        </InlineStack>
        <BlockStack :gap="400">
            <InlineStack :align="'center'">
                <BlockStack :gap="150">
                    <Text as="h2" variant="headingMd">{{ translatedTitle }}</Text>
                    <Text as="p" :alignment="'center'" variant="bodySm" :font-weight="'regular'">
                        {{ translatedMessage }}
                    </Text>
                </BlockStack>
            </InlineStack>
            <Box :padding-block-end="1600">
                <InlineStack :align="'center'" :gap="'200'">
                    <Button>{{ t(pagesLocaleVars.pageEmptyCreateBlank) }}</Button>
                    <Button variant="primary">{{ t(pagesLocaleVars.pageEmptyCreateTemplate) }}</Button>
                </InlineStack>
            </Box>
        </BlockStack>
    </Box>
</template>
<style lang="scss" scoped>
.empty-state-img {
    margin-block-start: 40px;
    width: 226px;
    height: 226px;
}
</style>
