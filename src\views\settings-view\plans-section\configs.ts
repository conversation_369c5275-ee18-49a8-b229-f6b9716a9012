import { settingsLocaleVars as vars } from '@/locales/settings';

import type { Plan, FaqItem } from './types';

export interface PlanFeature {
    name: string;
    free: string | boolean;
    payAsYouGo: string | boolean;
    enterprise: string | boolean;
}

export interface FeatureCategory {
    title: string;
    features: PlanFeature[];
}

export const plans: Plan[] = [
    {
        id: 'free',
        title: vars.freePlan,
        description: vars.planDescription,
        prices: {
            monthly: '$0',
            annual: '$0',
            frequency: {
                monthly: vars.month,
                annual: vars.year,
            },
        },
        features: [vars.processOrders, vars.amazingFeature, vars.anotherFeature, vars.customerSupport],
        buttonText: vars.selectPlan,
    },
    {
        id: 'pay-as-you-go',
        title: vars.payAsYouGoPlan,
        featuredText: vars.mostPopular,
        description: vars.planDescription,
        prices: {
            monthly: '$16.99',
            annual: '$14.99',
            frequency: {
                monthly: vars.month,
                annual: vars.year,
            },
            discount: 12,
        },
        features: [vars.processOrders, vars.amazingFeature, vars.anotherFeature, vars.customerSupport],
        buttonText: vars.selectPlan,
        hasSlider: true,
        publishedSlots: 12,
    },
    {
        id: 'enterprise',
        title: vars.enterprisePlan,
        description: vars.planDescription,
        prices: {
            monthly: '$88',
            annual: '$70',
            frequency: {
                monthly: vars.month,
                annual: vars.year,
            },
            discount: 20,
        },
        features: [vars.processOrders, vars.amazingFeature, vars.anotherFeature, vars.customerSupport],
        buttonText: vars.selectPlan,
        hasUnlimitedSlots: true,
    },
];

export const planFeatures: FeatureCategory[] = [
    {
        title: vars.slotCategory,
        features: [
            {
                name: vars.publishedFeature,
                free: '3 slot',
                payAsYouGo: '12 slot',
                enterprise: vars.unlimited,
            },
            {
                name: vars.draftFeature,
                free: '15 slot',
                payAsYouGo: vars.unlimited,
                enterprise: vars.unlimited,
            },
        ],
    },
    {
        title: vars.businessGrowthCategory,
        features: [
            {
                name: vars.abTestingFeature,
                free: '—',
                payAsYouGo: true,
                enterprise: true,
            },
        ],
    },
    {
        title: vars.templatesCategory,
        features: [
            {
                name: vars.croTemplatesFeature,
                free: '—',
                payAsYouGo: true,
                enterprise: true,
            },
        ],
    },
    {
        title: vars.pageTypesCategory,
        features: [
            {
                name: vars.regularPageFeature,
                free: true,
                payAsYouGo: true,
                enterprise: true,
            },
            {
                name: vars.homepageFeature,
                free: true,
                payAsYouGo: true,
                enterprise: true,
            },
            {
                name: vars.productPageFeature,
                free: true,
                payAsYouGo: true,
                enterprise: true,
            },
            {
                name: vars.collectionPageFeature,
                free: true,
                payAsYouGo: true,
                enterprise: true,
            },
            {
                name: vars.listCollectionsPageFeature,
                free: true,
                payAsYouGo: true,
                enterprise: true,
            },
        ],
    },
    {
        title: vars.supportCategory,
        features: [
            {
                name: vars.emailSupportFeature,
                free: true,
                payAsYouGo: true,
                enterprise: vars.priority,
            },
            {
                name: vars.liveChatFeature,
                free: true,
                payAsYouGo: true,
                enterprise: vars.priority,
            },
            {
                name: vars.teamViewFeature,
                free: '—',
                payAsYouGo: '—',
                enterprise: true,
            },
        ],
    },
];

export const faqItems: FaqItem[] = [
    {
        question: vars.faqQuestion1,
        answer: vars.faqAnswer1,
    },
    {
        question: vars.faqQuestion1,
        answer: vars.faqAnswer1,
    },
    {
        question: vars.faqQuestion1,
        answer: vars.faqAnswer1,
    },
    {
        question: vars.faqQuestion1,
        answer: vars.faqAnswer1,
    },
];
