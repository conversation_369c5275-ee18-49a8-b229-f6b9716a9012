import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';
import { fileURLToPath, URL } from 'node:url';
import { defineConfig } from 'vite';
// import vueDevTools from 'vite-plugin-vue-devtools'
import svgLoader from 'vite-svg-loader';

// https://vite.dev/config/
export default defineConfig({
    plugins: [
        vue({
            template: {
                compilerOptions: {
                    // treat all tags with a dash as custom elements
                    isCustomElement: (tag) => ['s-', 'ui-'].some((prefix) => tag.includes(prefix)),
                },
            },
        }),
        vueJsx(),
        // vueDevTools()
        svgLoader({
            svgoConfig: {
                multipass: true,
                plugins: [
                    {
                        name: 'preset-default',
                        params: {
                            overrides: {
                                // viewBox is required to resize SVGs with CSS.
                                // @see https://github.com/svg/svgo/issues/1128
                                removeViewBox: false,
                                cleanupIds: false,
                            },
                        },
                    },
                ],
            },
        }),
    ],
    resolve: {
        alias: {
            '@': fileURLToPath(new URL('./src', import.meta.url)),
            '@shopify/polaris-icons': fileURLToPath(
                new URL('./node_modules/@shopify/polaris-icons/dist/svg', import.meta.url),
            ),
            '@assets': fileURLToPath(new URL('./src/assets', import.meta.url)),
            '@components': fileURLToPath(new URL('./src/components', import.meta.url)),
            '@composables': fileURLToPath(new URL('./src/composables', import.meta.url)),
            '@configs': fileURLToPath(new URL('./src/configs', import.meta.url)),
            '@layouts': fileURLToPath(new URL('./src/layouts', import.meta.url)),
            '@locales': fileURLToPath(new URL('./src/locales', import.meta.url)),
            '@router': fileURLToPath(new URL('./src/router', import.meta.url)),
            '@services': fileURLToPath(new URL('./src/services', import.meta.url)),
            '@stores': fileURLToPath(new URL('./src/stores', import.meta.url)),
            '@types': fileURLToPath(new URL('./src/types', import.meta.url)),
            '@utils': fileURLToPath(new URL('./src/utils', import.meta.url)),
            '@views': fileURLToPath(new URL('./src/views', import.meta.url)),
        },
    },
    server: {
        https: {
            key: fileURLToPath(new URL('./ms-lpb-api-staging.matestore.io-key.pem', import.meta.url)),
            cert: fileURLToPath(new URL('./ms-lpb-api-staging.matestore.io.pem', import.meta.url)),
        },
        host: 'ms-lpb-api-staging.matestore.io',
        port: 443,
        proxy: {
            '^/api': {
                target: 'https://ms-lpb-api-staging.matestore.io/',
                changeOrigin: false,
                secure: true,
                ws: false,
            },
        },
        allowedHosts: true,
    },
    build: {
        outDir: 'dist',
        minify: 'esbuild',
    },
});
