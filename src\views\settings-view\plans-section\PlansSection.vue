<script setup lang="ts">
import { InlineStack, Button, Box, BlockStack, Icon, Text, InlineGrid } from '@ownego/polaris-vue';
import ChevronDownIcon from '@shopify/polaris-icons/ChevronDownIcon.svg';
import ChevronUpIcon from '@shopify/polaris-icons/ChevronUpIcon.svg';
import { ref, watch, nextTick, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';

import { settingsLocaleVars as vars } from '@/locales/settings';
import { usePlanStore } from '@/stores/planStore';

import { FeatureComparison } from './feature-comparison-block';
import { PlansPricingBlock } from './plans-pricing-block';

const { t } = useI18n();
const planStore = usePlanStore();
const toggleContainerRef = ref<HTMLElement | null>(null);
const sliderStyle = ref<{ width: string; left: string }>({ width: '0px', left: '0px' });

const updateSliderPosition = () => {
    nextTick(() => {
        if (!toggleContainerRef.value) return;

        const buttons = toggleContainerRef.value.querySelectorAll<HTMLButtonElement>('.billing__button');
        const activeButton = Array.from(buttons).find((btn) => btn.classList.contains('billing__button--active'));

        if (activeButton) {
            const containerRect = toggleContainerRef.value.getBoundingClientRect();
            const btnRect = activeButton.getBoundingClientRect();

            const left = btnRect.left - containerRect.left;
            const width = btnRect.width;

            sliderStyle.value = {
                width: `${width}px`,
                left: `${left}px`,
            };
        }
    });
};

watch(
    () => planStore.billingType,
    () => {
        updateSliderPosition();
    },
);

onMounted(async () => {
    await nextTick();
    updateSliderPosition();
});
</script>

<template>
    <InlineStack gap="400" :align="'center'" block-align="start">
        <Box padding="400" text-align="center">
            <div class="billing__toggle-wrapper">
                <div class="billing__toggle-container" :class="{ 'annual-active': planStore.billingType === 'annual' }">
                    <div class="billing__button-container">
                        <Button
                            class="billing__button"
                            :class="{ 'billing__button--active': planStore.billingType === 'monthly' }"
                            @click="planStore.setBillingType('monthly')"
                        >
                            {{ t(vars.monthly) }}
                        </Button>
                    </div>
                    <div class="billing__button-container">
                        <Button
                            class="billing__button"
                            :class="{ 'billing__button--active': planStore.billingType === 'annual' }"
                            @click="planStore.setBillingType('annual')"
                        >
                            {{ t(vars.annual) }}
                            <span class="billing__discount">{{ t(vars.savePercent) }}</span>
                        </Button>
                    </div>
                    <div class="billing__slider"></div>
                </div>
            </div>
        </Box>
    </InlineStack>

    <Box :padding-block="400">
        <InlineGrid gap="400" columns="3" block-align="start" class="plans-container">
            <PlansPricingBlock
                v-for="(plan, index) in planStore.mappedPlans"
                :sale="planStore.isSale"
                :key="index"
                :current-plan="planStore.currentPlanId"
                v-bind="plan"
            />
        </InlineGrid>
    </Box>

    <Box :padding-block-start="300">
        <InlineStack gap="400" align="center" block-align="start" class="plans-container">
            <Button @click="planStore.toggleFeatureComparison">
                <InlineStack :gap="'050'">
                    <Text as="span" variant="bodyMd">
                        {{ planStore.showFeatureComparison ? t(vars.hideFullPricing) : t(vars.seeFullPricing) }}
                    </Text>
                    <Icon :source="planStore.showFeatureComparison ? ChevronUpIcon : ChevronDownIcon" />
                </InlineStack>
            </Button>
        </InlineStack>
    </Box>

    <Box v-if="planStore.showFeatureComparison" :padding-block-start="800">
        <BlockStack gap="400">
            <FeatureComparison />
        </BlockStack>
    </Box>
</template>

<style scoped lang="scss">
.billing {
    &__toggle-wrapper {
        display: inline-block;
    }

    &__toggle-container {
        display: grid;
        grid-template-columns: 1fr 1fr;
        background-color: var(--m-gray-base);
        border-radius: 30px;
        position: relative;
        overflow: hidden;
        border: 1px solid var(--m-gray-base-20);

        &.annual-active .billing__slider {
            transform: translateX(100%);
        }
    }

    &__button-container {
        position: relative;
        z-index: 2;
    }

    &__slider {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 50%;
        background-color: var(--m-black-base);
        border-radius: 30px;
        transition: transform 0.3s ease;
        z-index: 1;
    }

    &__button {
        border: none !important;
        box-shadow: none !important;
        background-color: transparent !important;
        color: var(--m-black-base) !important;
        border-radius: 30px !important;
        min-width: 120px;
        padding: 8px 16px !important;
        height: 30px;
        margin: 0 !important;
        width: 100%;

        &--active {
            color: var(--m-surface-background) !important;

            .billing__discount {
                color: var(--m-surface-background) !important;
            }
        }
    }

    &__discount {
        color: var(--m-red-base) !important;
    }
}

.feature-comparison {
    &__title {
        font-size: 20px;
        font-weight: 600;
        margin: 0;
    }
}
</style>
