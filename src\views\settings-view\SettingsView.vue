<script setup lang="ts">
import { Page } from '@ownego/polaris-vue';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';

import { CmsHeader } from '@/components/cms-header';
import { settingsLocaleVars } from '@/locales/settings';

import { AnalyticSection } from './analytic-section';
import { GeneralSection } from './general-section';
import { PlansSection } from './plans-section';
import { UsageSection } from './usage-section';

const { t } = useI18n();

const activeTab = ref(0);

const settingsTabs = [
    { id: 'General', content: t(settingsLocaleVars.generalTab), panelID: 'general-section' },
    { id: 'Plans', content: t(settingsLocaleVars.plansTab), panelID: 'plans-section' },
    { id: 'Usages', content: t(settingsLocaleVars.usagesTab), panelID: 'usages-section' },
    { id: 'Analytics', content: t(settingsLocaleVars.analyticsTab), panelID: 'analytics-section' },
];

const handleTabChange = (tabId: number) => {
    activeTab.value = tabId;
};
</script>

<template>
    <Page :title="'Settings'">
        <CmsHeader :tabs="settingsTabs" :defaultActiveTab="0" @tab-change="handleTabChange" :isPage="false" />
        <div class="settings-container">
            <GeneralSection v-if="activeTab === 0" />
            <PlansSection v-if="activeTab === 1" />
            <UsageSection v-if="activeTab === 2" />
            <AnalyticSection v-if="activeTab === 3" />
        </div>
    </Page>
</template>

<style scoped lang="scss">
.settings-container {
    margin-top: 16px;
}
</style>
