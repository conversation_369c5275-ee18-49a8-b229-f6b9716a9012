import { computed, ref } from 'vue';

export function usePagination<T>(filteredData: () => T[], itemsPerPage = 3) {
    const currentPage = ref(1);
    const itemsPerPageValue = ref(itemsPerPage);

    const paginatedData = computed(() => {
        const start = (currentPage.value - 1) * itemsPerPageValue.value;
        const end = start + itemsPerPageValue.value;
        return filteredData().slice(start, end);
    });

    const totalPages = computed(() => Math.max(1, Math.ceil(filteredData().length / itemsPerPageValue.value)));

    const totalItems = computed(() => filteredData().length);

    const startItem = computed(() => (currentPage.value - 1) * itemsPerPageValue.value + 1);

    const endItem = computed(() => Math.min(startItem.value + itemsPerPageValue.value - 1, totalItems.value));

    const goToPreviousPage = () => {
        if (currentPage.value > 1) currentPage.value--;
    };

    const goToNextPage = () => {
        if (currentPage.value < totalPages.value) currentPage.value++;
    };

    const goToPage = (page: number) => {
        if (page >= 1 && page <= totalPages.value) {
            currentPage.value = page;
        }
    };

    const setItemsPerPage = (items: number) => {
        itemsPerPageValue.value = items;
        // Reset to first page when changing items per page
        currentPage.value = 1;
    };

    return {
        currentPage,
        paginatedData,
        totalPages,
        totalItems,
        startItem,
        endItem,
        itemsPerPage: itemsPerPageValue,
        goToPreviousPage,
        goToNextPage,
        goToPage,
        setItemsPerPage,
    };
}
