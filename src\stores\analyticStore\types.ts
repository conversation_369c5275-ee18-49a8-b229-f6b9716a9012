import type { <PERSON><PERSON><PERSON>, DateRange, DataPoint } from '@/views/analytic-view/analytic-detail-section/chart-block/types';

export type { DateRange, ChartItem, DataPoint };

export interface PageAnalytic {
    id: string;
    pageId: string;
    pageData: {
        title: string;
        status: 'Published' | 'Unpublished' | 'Draft';
        url: string;
    };
    status: 'Tracking' | 'NotTracking';
    addToCartRate: number;
    productViewsRate: number;
    visitors: number;
    sessions: number;
    Sales: number;
    Conversion_rate: number;
    items: ChartItem[];
    graphData: {
        sales: { date: string; value: number }[];
        comparison?: { date: string; value: number }[];
    };
    created_at: string;
    updated_at: string;
}

export interface PageWithAnalytics {
    id: string;
    title: string;
    status: string;
    type: string;
    url: string;
    updated_at: string;
    analytics: PageAnalytic;
}

export interface AnalyticState {
    items: ChartItem[];
    currentPeriod: DateRange;
    isLoading: boolean;
    error: string | null;
    comparisonMode: 'previous_period' | 'previous_year' | 'custom';
    graphData: {
        sales: { date: string; value: number }[];
        comparison?: { date: string; value: number }[];
    };
    analyticList: PageAnalytic[];
    currentAnalytic: PageAnalytic | null;

    queryValue: string;
    statusFilter: string[];
    typeFilter: string;
    currentPage: number;
    itemsPerPage: number;
}

export interface AnalyticGetters {
    getItemByKey: (_key: string) => ChartItem | undefined;
    formattedSalesData: DataPoint[];
    formattedComparisonData: DataPoint[] | null;
    totalRevenue: number;
    getAnalyticByPageId: (_pageId: string) => PageAnalytic | undefined;
    getAnalyticById: (_id: string) => PageAnalytic | undefined;

    filteredAnalytics: PageAnalytic[];
    paginatedAnalytics: PageAnalytic[];
    totalPages: number;
    totalAnalytics: number;
}

export interface AnalyticActions {
    fetchAnalyticData: (_period: DateRange) => Promise<void>;
    updateDateRange: (_range: DateRange) => void;
    setComparisonMode: (_mode: 'previous_period' | 'previous_year' | 'custom') => void;
    fetchPageAnalytics: () => Promise<void>;
    setCurrentAnalytic: (_analyticId: string) => void;
}
