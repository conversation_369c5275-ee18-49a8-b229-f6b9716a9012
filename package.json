{"name": "ms-lpb-dashboard-fe", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*", "format": "prettier --write src/", "prepare": "husky"}, "lint-staged": {"**/*.{jx,jsx,ts,tsx,vue,mts}": ["prettier --write", "eslint --fix", "oxlint --fix -D correctness"]}, "dependencies": {"@ownego/polaris-vue": "^2.1.31", "@oxlint/win32-x64": "^1.0.0", "@rollup/rollup-win32-x64-msvc": "^4.42.0", "@types/d3": "^7.4.3", "axios": "^1.9.0", "d3": "^7.9.0", "embla-carousel-vue": "^8.6.0", "eslint-plugin-perfectionist": "^4.15.0", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-i18n": "^11.1.5", "vue-router": "^4.5.0"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@tsconfig/node22": "^22.0.1", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.22.0", "eslint-plugin-oxlint": "^0.16.0", "eslint-plugin-vue": "~10.0.0", "husky": "^9.1.7", "jiti": "^2.4.2", "lint-staged": "^16.1.0", "npm-run-all2": "^7.0.2", "oxlint": "^0.16.0", "prettier": "3.5.3", "sass": "^1.89.2", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}