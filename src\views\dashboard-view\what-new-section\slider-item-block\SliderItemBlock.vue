<script setup lang="ts">
import { BlockStack, Box, Button, Card, InlineStack, Text } from '@ownego/polaris-vue';
import { useI18n } from 'vue-i18n';

import type { SliderItem } from '../configs';

const { t } = useI18n();

interface Props {
    sliderItem: SliderItem;
}

const props = defineProps<Props>();

const handleButtonClick = () => {
    if (props.sliderItem.link) {
        window.open(props.sliderItem.link, '_blank');
    }
};
</script>
<template>
    <div class="slider-item">
        <Card padding="none">
            <InlineStack :align="'start'">
                <div class="slider-item__image">
                    <img
                        :alt="sliderItem.imageAlt"
                        width="270px"
                        height="180px"
                        :src="sliderItem.imageUrl"
                        :style="{ objectFit: 'cover', objectPosition: 'center' }"
                    />
                </div>
                <div class="slider-item__content">
                    <BlockStack :gap="'400'">
                        <BlockStack :gap="'100'">
                            <Text :as="'h2'" :variant="'headingSm'">{{ t(sliderItem.title) }}</Text>
                            <Text :as="'p'" :variant="'bodyMd'">
                                {{ t(sliderItem.description) }}
                            </Text>
                        </BlockStack>
                        <Box>
                            <Button @click="handleButtonClick">{{ t(sliderItem.buttonText) }}</Button>
                        </Box>
                    </BlockStack>
                </div>
            </InlineStack>
        </Card>
    </div>
</template>
<style scoped lang="scss">
.slider-item {
    &__image {
        flex-shrink: 0;

        img {
            display: block;
        }
    }

    &__content {
        flex: 1;
        display: block;
        padding: 16px;
        min-height: 180px;
    }
}

@media (max-width: 768px) {
    .slider-item {
        &__image {
            img {
                width: 200px;
                height: 130px;
            }
        }
    }
}

@media (max-width: 480px) {
    .slider-item {
        :deep(.Polaris-InlineStack) {
            flex-direction: column;
        }

        &__image {
            width: 100%;

            img {
                width: 100%;
                height: 200px;
            }
        }
    }
}
</style>
