import { dashboardLocaleVars } from '@locales/dashboard';
import { nanoid } from 'nanoid';

export const setupGuideItems = [
    {
        id: nanoid(),
        titleKey: dashboardLocaleVars.setupItemAddProduct,
        descriptionKey: dashboardLocaleVars.setupItemAddProductDesc,
        image: {
            url: 'https://cdn.shopify.com/shopifycloud/shopify/assets/admin/home/<USER>/shop_pay_task-70830ae12d3f01fed1da23e607dc58bc726325144c29f96c949baca598ee3ef6.svg',
            alt: 'Illustration highlighting ShopPay integration',
        },
        complete: false,
        primaryButton: {
            contentKey: dashboardLocaleVars.setupItemAddProductBtn,
            props: {
                url: 'https://www.example.com',
                external: true,
            },
        },
        secondaryButton: {
            contentKey: dashboardLocaleVars.setupItemImportProductsBtn,
            props: {
                url: 'https://www.example.com',
                external: true,
            },
        },
    },
    {
        id: nanoid(),
        titleKey: dashboardLocaleVars.setupItemShareStore,
        descriptionKey: dashboardLocaleVars.setupItemShareStoreDesc,
        image: {
            url: 'https://cdn.shopify.com/shopifycloud/shopify/assets/admin/home/<USER>/detail-images/home-onboard-share-store-b265242552d9ed38399455a5e4472c147e421cb43d72a0db26d2943b55bdb307.svg',
            alt: "Illustration showing an online storefront with a 'share' icon in top right corner",
        },
        complete: false,
        primaryButton: {
            contentKey: dashboardLocaleVars.setupItemCopyLinkBtn,
            props: {
                onAction: () => console.log('copied store link!'),
            },
        },
    },
    {
        id: nanoid(),
        titleKey: dashboardLocaleVars.setupItemTranslate,
        descriptionKey: dashboardLocaleVars.setupItemTranslateDesc,
        image: {
            url: 'https://cdn.shopify.com/b/shopify-guidance-dashboard-public/nqjyaxwdnkg722ml73r6dmci3cpn.svgz',
        },
        complete: false,
        primaryButton: {
            contentKey: dashboardLocaleVars.setupItemAddLanguageBtn,
            props: {
                url: 'https://www.example.com',
                external: true,
            },
        },
    },
];
