<script setup lang="ts">
import { Divider, IndexFilters, IndexTable, useIndexResourceState, useSetIndexFiltersMode } from '@ownego/polaris-vue';
import QuestionCircleIcon from '@shopify/polaris-icons/QuestionCircleIcon.svg';
import { ref, computed, h, resolveComponent } from 'vue';
import { useI18n } from 'vue-i18n';

import { pagesLocaleVars } from '@/locales/pages';

import { filterChoices, typeFilterChoices, metricFilterChoices } from '../configs';
import { useFilters } from '../utils';
import { ListRowSnippet } from './list-row-snippet';

const { t } = useI18n();
const sortSelected = ref(['order asc']);
const { mode, setMode } = useSetIndexFiltersMode();
const props = defineProps<{
    Page: any[];
    resourceName: any;
    tableHeadings: any[];
    PageStatusOptions: any[];
    selected: number;
    status: string[];
    handleFiltersSelect: (_value: any) => void;
    handleStatus: (_value: string[]) => void;
    handleFiltersQueryChange: (_value: any) => void;
    appliedFilters: any[];
    updateStatus: (_ids: string[], _newStatus: string) => void;
}>();

const { queryValue, handleQueryValueRemove, handleFiltersClearAll } = useFilters();
const currentPageData = computed(() => props.Page);
const { selectedResources, allResourcesSelected, handleSelectionChange, clearSelection } =
    useIndexResourceState(currentPageData);

defineExpose({
    clearSelection,
});

const filters = computed(() => [
    {
        name: 'status',
        label: 'Analytics Status',
        filter: () =>
            h(resolveComponent('ChoiceList'), {
                title: 'Analytics Status',
                titleHidden: true,
                choices: filterChoices.map((choice) => ({
                    ...choice,
                    label: choice.label,
                })),
                modelValue: props.status || [],
                onChange: props.handleStatus,
                allowMultiple: true,
            }),
        shortcut: true,
    },
    {
        name: 'type',
        label: 'Page Type',
        filter: () =>
            h(resolveComponent('ChoiceList'), {
                title: 'Page Type',
                titleHidden: true,
                choices: typeFilterChoices.map((choice) => ({
                    ...choice,
                    label: choice.label,
                })),
                modelValue: [], // Will be connected to store later
                onChange: () => { }, // Will be connected to store later
                allowMultiple: true,
            }),
        shortcut: true,
    },
    {
        name: 'metric',
        label: 'Performance Metrics',
        filter: () =>
            h(resolveComponent('ChoiceList'), {
                title: 'Performance Metrics',
                titleHidden: true,
                choices: metricFilterChoices.map((choice) => ({
                    ...choice,
                    label: choice.label,
                })),
                modelValue: [], // Will be connected to store later
                onChange: () => { }, // Will be connected to store later
                allowMultiple: true,
            }),
        shortcut: false,
    },
]);

const onHandleCancel = () => { };

const handleFiltersSort = (value: string[]) => {
    sortSelected.value = value;
};

const translatedSortOptions = computed<any[]>(() => [
    { label: t(pagesLocaleVars.pageTableTitle), value: 'order asc', directionLabel: 'Ascending' },
    { label: t(pagesLocaleVars.pageTableTitle), value: 'order desc', directionLabel: 'Descending' },
    { label: t(pagesLocaleVars.pageTableLastUpdated), value: 'customer asc', directionLabel: 'A-Z' },
    { label: t(pagesLocaleVars.pageTableLastUpdated), value: 'customer desc', directionLabel: 'Z-A' },
]);

const translatedStatusOptions = computed(() =>
    props.PageStatusOptions.map((option) => ({
        ...option,
        content:
            option.content === 'All'
                ? 'All'
                : option.content === 'Tracking'
                    ? 'Tracking'
                    : option.content === 'Not Tracking'
                        ? 'Not Tracking'
                        : option.content,
    })),
);

const tableHeadingsList: any = [
    { title: 'Title' },
    { title: 'Status' },
    { title: 'A/B Testing' },

    {
        title: h('div', { style: 'display: flex; align-items: center; gap: 4px;' }, [
            'Add to cart rate',
            h(
                resolveComponent('Tooltip'),
                {
                    content: 'The percentage of sessions where a product is added to cart',
                    padding: '400',
                },
                {
                    default: () =>
                        h(resolveComponent('Icon'), {
                            source: QuestionCircleIcon,
                            tone: 'subdued',
                            style: 'width: 12px; height: 12px;',
                        }),
                },
            ),
        ]),
    },
    {
        title: h('div', { style: 'display: flex; align-items: center; gap: 4px;' }, [
            'Product views rate',
            h(
                resolveComponent('Tooltip'),
                {
                    content: 'The number of times a specific product is viewed by visitors within a specified period',
                    padding: '400',
                },
                {
                    default: () =>
                        h(resolveComponent('Icon'), {
                            source: QuestionCircleIcon,
                            tone: 'subdued',
                            style: 'width: 12px; height: 12px;',
                        }),
                },
            ),
        ]),
    },
    {
        title: h('div', { style: 'display: flex; align-items: center; gap: 4px;' }, [
            'Visitors',
            h(
                resolveComponent('Tooltip'),
                {
                    content: 'The total number of unique visitors to a page.',
                    padding: '400',
                },
                {
                    default: () =>
                        h(resolveComponent('Icon'), {
                            source: QuestionCircleIcon,
                            tone: 'subdued',
                            style: 'width: 12px; height: 12px;',
                        }),
                },
            ),
        ]),
    },
    {
        title: h('div', { style: 'display: flex; align-items: center; gap: 4px;' }, [
            'Sessions',
            h(
                resolveComponent('Tooltip'),
                {
                    content: 'The total number of sessions on a page.',
                    padding: '400',
                },
                {
                    default: () =>
                        h(resolveComponent('Icon'), {
                            source: QuestionCircleIcon,
                            tone: 'subdued',
                            style: 'width: 12px; height: 12px;',
                        }),
                },
            ),
        ]),
    },
    {
        title: h('div', { style: 'display: flex; align-items: center; gap: 4px;' }, [
            'Sales',
            h(
                resolveComponent('Tooltip'),
                {
                    content:
                        'Total gross sales generated by a page. This number is calculated based on the product price from complete orders.',
                    padding: '400',
                },
                {
                    default: () =>
                        h(resolveComponent('Icon'), {
                            source: QuestionCircleIcon,
                            tone: 'subdued',
                            style: 'width: 12px; height: 12px;',
                        }),
                },
            ),
        ]),
    },
    {
        title: h('div', { style: 'display: flex; align-items: center; gap: 4px;' }, [
            'Conversion Rate',
            h(
                resolveComponent('Tooltip'),
                {
                    content: 'The percentage of sessions where visitors click on a trackable element from the page.',
                    padding: '400',
                },
                {
                    default: () =>
                        h(resolveComponent('Icon'), {
                            source: QuestionCircleIcon,
                            tone: 'subdued',
                            style: 'width: 12px; height: 12px;',
                        }),
                },
            ),
        ]),
    },
];
</script>

<template>
    <div class="index-filters-container">
        <IndexFilters :sortOptions="translatedSortOptions" :sortSelected="sortSelected" :queryValue="queryValue"
            :queryPlaceholder="t(pagesLocaleVars.pageSearchPlaceholder)"
            :cancelAction="{ onAction: onHandleCancel, disabled: false, loading: false }"
            :tabs="translatedStatusOptions" :selected="selected" :filters="filters" :appliedFilters="appliedFilters"
            :mode="mode" @set-mode="setMode" @query-change="handleFiltersQueryChange"
            @query-clear="handleQueryValueRemove" @sort="handleFiltersSort" @select="handleFiltersSelect"
            @clear-all="handleFiltersClearAll" :can-create-new-view="false" />
    </div>
    <IndexTable :condensed="false" :resourceName="resourceName" :itemCount="Page.length"
        :selectedItemsCount="allResourcesSelected ? 'All' : selectedResources.length"
        @selection-change="handleSelectionChange" :headings="tableHeadingsList" :selectable="false">
        <ListRowSnippet v-for="(Page, index) in props.Page" :key="Page.id" :Page="Page" :index="index"
            :selected="selectedResources.includes(String(Page.id))" />
    </IndexTable>

    <Divider borderColor="border" />
</template>

<style scoped lang="scss">
.index-filters-container {
    .Polaris-Text--root {
        color: var(--m-black-base);
    }
}
</style>
