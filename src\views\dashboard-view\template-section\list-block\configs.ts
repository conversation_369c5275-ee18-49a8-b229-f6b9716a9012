export interface TemplateItem {
    id: string;
    name: string;
    used: number;
    selected: boolean;
    thumbnail: string;
    category: string;
}

export const templateList: TemplateItem[] = [
    {
        id: 'template-1',
        name: 'Blank',
        used: 100,
        selected: false,
        thumbnail: 'img-1.png',
        category: 'regular',
    },
    {
        id: 'template-2',
        name: 'Frequently bought together',
        used: 200,
        selected: false,
        thumbnail: 'img-2.png',
        category: 'product',
    },
    {
        id: 'template-3',
        name: 'Product Bundle',
        used: 180,
        selected: false,
        thumbnail: 'img-3.png',
        category: 'product',
    },
    {
        id: 'template-4',
        name: 'Home Banner',
        used: 600,
        selected: false,
        thumbnail: 'img-4.png',
        category: 'home',
    },
    {
        id: 'template-5',
        name: 'Collection Grid',
        used: 300,
        selected: false,
        thumbnail: 'img-5.png',
        category: 'collection',
    },
    {
        id: 'template-6',
        name: 'Collection List View',
        used: 400,
        selected: false,
        thumbnail: 'img-6.png',
        category: 'list-collection',
    },
];
