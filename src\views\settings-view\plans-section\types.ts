export interface PlanPrice {
    monthly: string;
    annual: string;
    frequency: {
        monthly: string;
        annual: string;
    };
    discount?: number;
}

export interface Plan {
    id: string;
    title: string;
    description: string;
    featuredText?: string;
    specialLabel?: string;
    prices: PlanPrice;
    features: string[];
    buttonText?: string;
    hasSlider?: boolean;
    publishedSlots?: number;
    hasUnlimitedSlots?: boolean;
}

export interface FaqItem {
    question: string;
    answer: string;
}
