<script setup lang="ts">
import { BlockStack, Card, Text, InlineStack, Box, Button, Badge, ButtonGroup, RangeSlider } from '@ownego/polaris-vue';
import { computed, ref, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';

import { settingsLocaleVars as vars } from '@/locales/settings';
import { usePlanStore } from '@/stores/planStore';

const { t } = useI18n();
const planStore = usePlanStore();

const props = defineProps<{
    title: string;
    description?: string;
    type: string;
    price: {
        monthly: string;
        annual: string;
    };
    frequency: string;
    features: string[];
    featuredText?: string;
    specialLabel?: string;
    sale?: boolean;
    sale_price?: number;
    hasSlider?: boolean;
    publishedSlots?: number;
    hasUnlimitedSlots?: boolean;
    button?: {
        content: string;
        props: Record<string, any>;
    };
    currentPlan?: string;
}>();

// Biến tạm thời để lưu giá trị slider
const tempSlotValue = ref(
    props.type === 'pay-as-you-go' ? planStore.selectedSlots : (props.publishedSlots ?? planStore.MIN_SLOTS),
);

// Khởi tạo giá trị khi component được mount
onMounted(() => {
    if (props.type === 'pay-as-you-go') {
        tempSlotValue.value = planStore.selectedSlots;
    }
});

// Sử dụng giá trị tạm thời thay vì cập nhật trực tiếp vào store
const slotValue = computed({
    get: () => tempSlotValue.value,
    set: (value: number) => {
        tempSlotValue.value = value;
    },
});

const calculateExtraPrice = (slots: number) => {
    return planStore.calculateExtraPrice(slots);
};

const BASE_MAX_PRICE_MONTHLY = 88;
const BASE_MAX_PRICE_ANNUAL = 70;

const maxPriceBySale = computed(() => {
    const hasSale = (props.sale_price ?? 0) > 0;
    return hasSale ? BASE_MAX_PRICE_ANNUAL * 0.9 : BASE_MAX_PRICE_MONTHLY * 0.9;
});

const finalPrice = computed(() => {
    const priceString = props.sale ? props.price.annual : props.price.monthly;
    let base = parseFloat(priceString.replace(/[^0-9.]/g, ''));
    if (isNaN(base)) base = 16;

    const extra = props.hasSlider ? calculateExtraPrice(tempSlotValue.value) : 0;
    const total = base + extra;

    if (props.hasSlider) {
        const maxPrice = maxPriceBySale.value;
        return total < maxPrice ? total.toFixed(2) : maxPrice.toFixed(2);
    }
    return total.toFixed(2);
});

const discountPercentage = computed(() => props.sale_price ?? 0);

const getPlanLevel = (planType: string): number => {
    switch (planType) {
        case 'free':
            return 1;
        case 'pay-as-you-go':
            return 2;
        case 'enterprise':
            return 3;
        default:
            return 0;
    }
};

const shouldShowDowngrade = computed(() => {
    if (!props.currentPlan) return false;
    return getPlanLevel(props.type) < getPlanLevel(props.currentPlan);
});

const isLoading = ref(false);

const handleClick = async () => {
    if (isLoading.value) return;
    isLoading.value = true;

    try {
        if (props.type === 'pay-as-you-go') {
            planStore.setSelectedSlots(tempSlotValue.value);
        }

        await new Promise((resolve) => setTimeout(resolve, 2000));
        if (typeof props.button?.props?.onClick === 'function') {
            await props.button.props.onClick();
        }
    } catch (error) {
        console.error(error);
    } finally {
        isLoading.value = false;
    }
};
</script>

<template>
    <div class="plan__card" :class="{ 'plan__card--featured': props.featuredText }">
        <div v-if="props.featuredText" class="plan__badge--featured">
            <div class="badge-background"></div>
            <Badge size="large">
                <div class="plan__badge-text--featured">{{ t(props.featuredText) }}</div>
            </Badge>
        </div>

        <div v-if="props.currentPlan === props.type" class="plan__badge--special">
            <Badge progress="complete" tone="success">
                <div class="plan__badge-text--special">{{ t(vars.current) }}</div>
            </Badge>
        </div>

        <Card class="plan__container">
            <div class="plan__content">
                <div class="plan__top">
                    <BlockStack gap="200" align="start">
                        <Text as="h3" variant="headingLg">{{ t(props.title) }}</Text>
                        <Text v-if="props.description" as="p" variant="bodySm" tone="subdued">
                            {{ t(props.description) }}
                        </Text>
                    </BlockStack>

                    <div v-if="props.sale_price && props.sale">
                        <InlineStack gap="150">
                            <div class="plan__price--original">{{ props.price.monthly }}</div>
                            <div class="plan__discount">{{ discountPercentage }}% {{ t(vars.off) }}</div>
                        </InlineStack>
                    </div>

                    <InlineStack block-align="end" gap="100" align="start">
                        <Text as="h2" variant="heading2xl">${{ finalPrice }}</Text>
                        <Box padding-block-end="200">
                            <Text as="span" variant="bodySm">/ {{ t(props.frequency) }}</Text>
                        </Box>
                    </InlineStack>

                    <Box v-if="props.hasSlider">
                        <Box :min-height="'20px'" :padding-block-start="'100'">
                            <RangeSlider
                                class="range-slider"
                                v-model="slotValue"
                                :min="planStore.MIN_SLOTS"
                                :max="planStore.MAX_SLOTS"
                                :step="5"
                                output
                            />
                        </Box>

                        <div class="plan__slots">
                            {{ slotValue }} {{ t(vars.publishedSlots) }}<span class="plan__slots__asterisk">*</span>
                        </div>
                    </Box>

                    <div v-if="props.hasUnlimitedSlots" class="plan__slots--unlimited">
                        {{ t(vars.unlimitedPublishedSlots) }}
                    </div>

                    <BlockStack gap="100">
                        <Text
                            v-for="(feature, index) in props.features"
                            :key="index"
                            tone="subdued"
                            as="p"
                            variant="bodyMd"
                        >
                            {{ t(feature) }}
                        </Text>
                    </BlockStack>
                </div>

                <div class="plan__bottom">
                    <ButtonGroup v-if="props.button && props.currentPlan !== props.type" fullWidth>
                        <Button
                            v-bind="{ ...props.button.props, onClick: undefined }"
                            :loading="isLoading"
                            :variant="shouldShowDowngrade ? undefined : 'primary'"
                            @click="handleClick"
                        >
                            {{ shouldShowDowngrade ? t(vars.downgrade) : t(props.button.content) }}
                        </Button>
                    </ButtonGroup>
                </div>
            </div>
        </Card>
    </div>
</template>

<style>
.range-slider div[class*='Polaris-RangeSlider--trackDashedAfter']::after {
    background-color: var(--m-contrast-125) !important;
    background-image: none !important;
}
</style>

<style scoped lang="scss">
.plan {
    &__card {
        flex: 1 1 100%;
        display: flex;
        flex-direction: column;
        position: relative;
        border-radius: 0.75rem;
        z-index: 0;
        min-width: 280px;
        height: 100%;

        &--featured {
            box-shadow: 0px 0px 15px 4px var(--m-purple-base-70);
            filter: drop-shadow(0px 0px 15px var(--m-purple-base-70));
        }
    }

    &__badge {
        &--featured {
            position: absolute;
            top: -15px;
            right: 6px;
            z-index: 1000;
            overflow: visible;

            .badge-background {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(255, 255, 255, 1);
                border-radius: 8px;
                z-index: 0;
                filter: drop-shadow(0px 0px 7px rgba(128, 81, 255, 1));
                box-shadow: 0 0 6px 2px rgba(128, 81, 255, 0.4);
            }

            .Polaris-Badge {
                background-color: rgba(128, 81, 255, 0.25);
                border-radius: 8px;
                box-shadow: none;
                position: relative;
                z-index: 1;
            }
        }

        &--special {
            position: absolute;
            top: 16px;
            right: 16px;
            z-index: 100;
        }
    }

    &__badge-text {
        &--featured {
            color: var(--m-purple-base);
            position: relative;
            z-index: 2;
        }

        &--special {
            color: var(--m-success-base);
        }
    }

    &__container {
        flex: 1;
        display: flex;
        flex-direction: column;
        height: 22px;
    }

    &__content {
        display: flex;
        flex-direction: column;
        flex: 1;
        height: 100%;
    }

    &__top {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 16px;
    }

    &__bottom {
        margin-top: auto;
        padding-top: 24px;
    }

    &__price {
        &--original {
            text-decoration: line-through;
            color: var(--m-black-sale);
            font-size: 13px;
        }
    }

    &__discount {
        background-color: var(--m-shared-base-10);
        color: var(--m-shared-base);
        font-size: 12px;
        font-weight: 450;
        padding: 4px 8px;
        border-radius: 16px;
        height: 22px;
        line-height: 15px;
    }

    &__slots {
        font-size: 13px;
        color: var(--m-shared-base);
        font-weight: 500;
        margin-top: 12px;

        &--unlimited {
            font-size: 13px;
            color: var(--m-shared-base);
            font-weight: 500;
        }

        &__asterisk {
            color: var(--m-shared-base);
        }
    }
}
</style>
