<script setup lang="ts">
import { Text } from '@ownego/polaris-vue';
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';

import { getFlagUrl } from '@/utils/base';

import { menuItems, languages } from './configs';

const { locale, t } = useI18n();

const activeTooltipId = ref<string | null>(null);
const showTooltip = (id: string) => (activeTooltipId.value = id);
const hideTooltip = () => (activeTooltipId.value = null);

const showLangPopover = ref(false);
const toggleLangPopover = () => {
    showLangPopover.value = !showLangPopover.value;
};

const currentLanguage = computed(() => {
    return languages.find((lang) => lang.language === locale.value) || languages[0];
});

const changeLanguage = (langCode: string) => {
    locale.value = langCode;
    showLangPopover.value = false;
    activeTooltipId.value = null;
};
</script>

<template>
    <div class="mini-menu-section">
        <div class="mini-menu-section__container">
            <div
                v-for="item in menuItems"
                :key="item.id"
                class="mini-menu-section__item"
                @click="item.id === 'language' ? toggleLangPopover() : item.onClick?.()"
                @mouseenter="showTooltip(item.id)"
                @mouseleave="hideTooltip"
            >
                <div class="mini-menu-section__icon-wrapper">
                    <div v-if="item.id === 'profile'" class="mini-menu-section__avatar">T</div>

                    <div v-else-if="item.id === 'language'" class="mini-menu-section__language">
                        <img :src="getFlagUrl(currentLanguage.country)" :alt="currentLanguage.name" />
                    </div>

                    <component v-else :is="item.icon" class="mini-menu-section__icon" />

                    <div v-if="item.badge" class="mini-menu-section__badge">
                        {{ item.badge }}
                    </div>
                </div>

                <div v-if="activeTooltipId === item.id" class="mini-menu-section__tooltip">
                    <Text :as="'span'" :variant="'bodyMd'">{{ t(item.labelKey) }}</Text>
                </div>

                <div v-if="item.id === 'language' && showLangPopover" class="mini-menu-section__popover">
                    <div
                        v-for="lang in languages"
                        :key="lang.language"
                        :class="[
                            'mini-menu-section__lang-option',
                            { 'mini-menu-section__lang-option--active': lang.language === locale },
                        ]"
                        @click.stop="changeLanguage(lang.language)"
                    >
                        <img :src="getFlagUrl(lang.country)" :alt="`${lang.name} flag`" />
                        <span>{{ lang.name }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped lang="scss">
.mini-menu-section {
    position: fixed;
    right: 25px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 100;

    @media screen and (max-width: 768px) {
        right: 10px;
    }

    &__container {
        background-color: var(--m-surface-background);
        border-radius: 16px;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 10px 0;
        width: 40px;
    }

    &__item {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        cursor: pointer;
        margin: 5px 0;
        position: relative;
        transition: all 0.2s ease;
    }

    &__icon-wrapper {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 24px;
        height: 24px;
        border-radius: 50% !important;
    }

    &__avatar {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background-color: var(--m-primary-base);
        color: var(--m-surface-foreground);
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
    }

    &__language {
        width: 14px;
        height: 14px;
        border-radius: 50%;
        overflow: hidden;
        display: flex;
        justify-content: center;
        align-items: center;

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }

    &__icon {
        width: 20px;
        height: 20px;
        color: var(--p-color-icon);
    }

    &__badge {
        position: absolute;
        top: -5px;
        right: -5px;
        background-color: var(--p-color-bg-fill-info);
        color: var(--m-surface-background);
        border-radius: 50%;
        width: 16px;
        height: 16px;
        font-size: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: bold;
    }

    &__tooltip {
        position: absolute;
        right: 48px;
        top: 50%;
        transform: translateY(-50%);
        background-color: var(--m-surface-background);
        color: var(--m-surface-foreground);
        padding: 6px 12px;
        border-radius: 4px;
        white-space: nowrap;
        z-index: 1000;
        pointer-events: none;

        &:after {
            content: '';
            position: absolute;
            top: 50%;
            right: -6px;
            transform: translateY(-50%);
            border-width: 6px 0 6px 6px;
            border-style: solid;
            border-color: transparent transparent transparent var(--m-surface-background);
        }
    }

    &__popover {
        position: absolute;
        right: 48px;
        top: 50%;
        transform: translateY(-50%);
        background-color: var(--m-surface-background);
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
        border-radius: 8px;
        padding: 4px 0;
        z-index: 1000;
        min-width: 120px;
        border: 1px solid var(--m-border-subdued);
    }

    &__lang-option {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 6px 12px;
        cursor: pointer;
        transition: background 0.2s ease;
        font-size: 13px;

        img {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            object-fit: cover;
            border: 1px solid var(--m-border-subdued);
        }

        span {
            font-weight: 500;
        }

        &:hover {
            background-color: var(--m-surface-subdued);
        }

        &--active {
            background-color: var(--m-contrast-150);

            span {
                font-weight: 600;
            }
        }
    }
}
</style>
