import vars from '@locales/dashboard/vars';
import NotificationIcon from '@shopify/polaris-icons/NotificationIcon.svg';
import PlanIcon from '@shopify/polaris-icons/PlanIcon.svg';
import HelpIcon from '@shopify/polaris-icons/QuestionCircleIcon.svg';
export interface MenuItem {
    id: string;
    icon: any;
    labelKey: string;
    badge?: number;
    onClick?: () => void;
}

export interface Language {
    language: string;
    country: string;
    name: string;
}

export const menuItems: MenuItem[] = [
    {
        id: 'profile',
        icon: null,
        labelKey: vars.miniMenuProfile,
        onClick: () => console.log('Profile clicked'),
    },
    {
        id: 'notifications',
        icon: NotificationIcon,
        labelKey: vars.miniMenuNotifications,
        badge: 5,
        onClick: () => console.log('Notifications clicked'),
    },
    {
        id: 'store',
        icon: PlanIcon,
        labelKey: vars.miniMenuStore,
        onClick: () => console.log('Store clicked'),
    },
    {
        id: 'help',
        icon: HelpIcon,
        labelKey: vars.miniMenuHelp,
        onClick: () => console.log('Help clicked'),
    },
    {
        id: 'language',
        icon: null,
        labelKey: vars.miniMenuLanguage,
        onClick: () => console.log('Language clicked'),
    },
];

export const languages: Language[] = [
    { language: 'en', country: 'us', name: 'English' },
    { language: 'vi', country: 'vn', name: 'Vietnamese' },
];
