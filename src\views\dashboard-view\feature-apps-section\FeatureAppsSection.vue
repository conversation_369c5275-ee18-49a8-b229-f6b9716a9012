<script setup lang="ts">
declare const puzzleApp: {
    dismissSection: (_section: HTMLElement) => void;
};
const dismissFeaturedAppsSection = () => {
    const section = document.getElementById('featured-apps-section');
    if (section) {
        puzzleApp?.dismissSection(section as HTMLElement);
    }
};
</script>

<template>
    <s-section id="featured-apps-section">
        <s-grid gridTemplateColumns="1fr auto" alignItems="center" paddingBlockEnd="small-400">
            <s-heading>Featured apps</s-heading>
            <s-button
                :onClick="dismissFeaturedAppsSection"
                icon="x"
                tone="neutral"
                variant="tertiary"
                accessibilityLabel="Dismiss featured apps section"
            />
        </s-grid>

        <s-grid gridTemplateColumns="repeat(auto-fit, minmax(240px, 1fr))" gap="base">
            <!-- Shopify Flow card -->
            <s-clickable
                href="https://apps.shopify.com/flow"
                border="base"
                borderRadius="base"
                padding="base"
                inlineSize="100%"
                accessibilityLabel="Download Shopify Flow"
            >
                <s-grid gridTemplateColumns="auto 1fr auto" alignItems="stretch" gap="base">
                    <s-box border="base" borderRadius="base" overflow="hidden" maxInlineSize="40px" maxBlockSize="40px">
                        <s-image
                            src="https://cdn.shopify.com/app-store/listing_images/15100ebca4d221b650a7671125cd1444/icon/CO25r7-jh4ADEAE=.png"
                            alt="Shopify Flow icon"
                        />
                    </s-box>
                    <s-box>
                        <s-heading>Shopify Flow</s-heading>
                        <s-paragraph>Free</s-paragraph>
                        <s-paragraph> Automate everything and get back to business. </s-paragraph>
                    </s-box>
                    <s-stack justifyContent="start">
                        <s-button
                            href="https://apps.shopify.com/flow"
                            icon="download"
                            accessibilityLabel="Download Shopify Flow"
                        />
                    </s-stack>
                </s-grid>
            </s-clickable>

            <!-- Shopify Planet card (x2) -->
            <s-clickable
                href="https://apps.shopify.com/planet"
                border="base"
                borderRadius="base"
                padding="base"
                inlineSize="100%"
                accessibilityLabel="Download Shopify Planet"
            >
                <s-grid gridTemplateColumns="auto 1fr auto" alignItems="stretch" gap="base">
                    <s-box border="base" borderRadius="base" overflow="hidden" maxInlineSize="40px" maxBlockSize="40px">
                        <s-image
                            src="https://cdn.shopify.com/app-store/listing_images/87176a11f3714753fdc2e1fc8bbf0415/icon/CIqiqqXsiIADEAE=.png"
                            alt="Shopify Planet icon"
                        />
                    </s-box>
                    <s-box>
                        <s-heading>Shopify Planet</s-heading>
                        <s-paragraph>Free</s-paragraph>
                        <s-paragraph> Offer carbon-neutral shipping and showcase your commitment. </s-paragraph>
                    </s-box>
                    <s-stack justifyContent="start">
                        <s-button
                            href="https://apps.shopify.com/planet"
                            icon="download"
                            accessibilityLabel="Download Shopify Planet"
                        />
                    </s-stack>
                </s-grid>
            </s-clickable>

            <s-clickable
                href="https://apps.shopify.com/planet"
                border="base"
                borderRadius="base"
                padding="base"
                inlineSize="100%"
                accessibilityLabel="Download Shopify Planet"
            >
                <s-grid gridTemplateColumns="auto 1fr auto" alignItems="stretch" gap="base">
                    <s-box border="base" borderRadius="base" overflow="hidden" maxInlineSize="40px" maxBlockSize="40px">
                        <s-image
                            src="https://cdn.shopify.com/app-store/listing_images/87176a11f3714753fdc2e1fc8bbf0415/icon/CIqiqqXsiIADEAE=.png"
                            alt="Shopify Planet icon"
                        />
                    </s-box>
                    <s-box>
                        <s-heading>Shopify Planet</s-heading>
                        <s-paragraph>Free</s-paragraph>
                        <s-paragraph> Offer carbon-neutral shipping and showcase your commitment. </s-paragraph>
                    </s-box>
                    <s-stack justifyContent="start">
                        <s-button
                            href="https://apps.shopify.com/planet"
                            icon="download"
                            accessibilityLabel="Download Shopify Planet"
                        />
                    </s-stack>
                </s-grid>
            </s-clickable>
        </s-grid>
    </s-section>
</template>
