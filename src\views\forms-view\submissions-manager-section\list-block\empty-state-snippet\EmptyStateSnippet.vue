<script setup lang="ts">
import EmptyStateImg from '@assets/svgs/empty-state-img.svg';
import { Box, InlineStack, BlockStack, Button, Text } from '@ownego/polaris-vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';

import vars from '@/locales/forms/vars';

const { t } = useI18n();
const router = useRouter();
</script>

<template>
    <Box>
        <InlineStack :align="'center'">
            <div class="empty-state-img">
                <EmptyStateImg />
            </div>
        </InlineStack>
        <BlockStack :gap="400">
            <InlineStack :align="'center'">
                <BlockStack :gap="150">
                    <Text as="h2" variant="headingMd">{{ t(vars.submissionsEmptyTitle) }}</Text>
                    <Text as="p" :alignment="'center'" variant="bodySm" :font-weight="'regular'">
                        {{ t(vars.submissionsEmptyMessage) }}
                    </Text>
                </BlockStack>
            </InlineStack>
            <Box :padding-block-end="1600">
                <InlineStack :align="'center'" :gap="'200'">
                    <Button variant="primary" @click="router.push('/forms')">
                        {{ t(vars.submissionsGoBack) }}
                    </Button>
                </InlineStack>
            </Box>
        </BlockStack>
    </Box>
</template>
<style lang="scss" scoped>
.empty-state-img {
    margin-block-start: 40px;
    width: 226px;
    height: 226px;
}
</style>
