import { dashboardLocaleVars } from '@locales/dashboard';
import ChatIcon from '@shopify/polaris-icons/ChatIcon.svg';
import QuestionIcon from '@shopify/polaris-icons/QuestionCircleIcon.svg';
import SendIcon from '@shopify/polaris-icons/SendIcon.svg';
import TeamIcon from '@shopify/polaris-icons/TeamIcon.svg';

export const supportItems = [
    {
        titleKey: dashboardLocaleVars.supportLiveChatTitle,
        descriptionKey: dashboardLocaleVars.supportLiveChatDesc,
        icon: ChatIcon,
        primaryButton: {
            contentKey: dashboardLocaleVars.supportLiveChatBtn,
            onClick: () => console.log('Chat now clicked'),
        },
        secondaryButton: {
            contentKey: dashboardLocaleVars.supportEmailBtn,
            onClick: () => console.log('Send email clicked'),
        },
    },
    {
        titleKey: dashboardLocaleVars.supportHelpCenterTitle,
        descriptionKey: dashboardLocaleVars.supportHelpCenterDesc,
        icon: QuestionIcon,
        primaryButton: {
            contentKey: dashboardLocaleVars.supportHelpCenterBtn,
            onClick: () => console.log('Visit now clicked'),
        },
        secondaryButton: null,
    },
    {
        titleKey: dashboardLocaleVars.supportCommunityTitle,
        descriptionKey: dashboardLocaleVars.supportCommunityDesc,
        icon: TeamIcon,
        primaryButton: {
            contentKey: dashboardLocaleVars.supportCommunityBtn,
            onClick: () => console.log('Join now clicked'),
        },
        secondaryButton: null,
    },
    {
        titleKey: dashboardLocaleVars.supportFeatureRequestTitle,
        descriptionKey: dashboardLocaleVars.supportFeatureRequestDesc,
        icon: SendIcon,
        primaryButton: {
            contentKey: dashboardLocaleVars.supportFeatureRequestBtn,
            url: 'https://example.com/help',
            external: true,
        },
        secondaryButton: null,
    },
];
