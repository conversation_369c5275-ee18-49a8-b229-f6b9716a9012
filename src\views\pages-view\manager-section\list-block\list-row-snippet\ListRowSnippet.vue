<script setup lang="ts">
import { IndexTableRow, IndexTableCell, Text, BlockStack, Badge, InlineStack, Icon, Box } from '@ownego/polaris-vue';
import ChartPopularIcon from '@shopify/polaris-icons/ChartPopularIcon.svg';
import ComposeIcon from '@shopify/polaris-icons/ComposeIcon.svg';
import ViewIcon from '@shopify/polaris-icons/ViewIcon.svg';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';

import { pagesLocaleVars } from '@/locales/pages';

import type { ListRowSnippetProps } from './types';

const { t } = useI18n();
const router = useRouter();
const props = defineProps<ListRowSnippetProps>();
const handleRowClick = (event: Event) => {
    event.stopPropagation();
    const modal = document.getElementById('test-modal') as HTMLDialogElement;
    if (modal) {
        modal.show();
    }
};

const handleIconClick = (event: Event) => {
    event.stopPropagation();
};

const getTranslatedStatus = computed(() => {
    return props.Page.status === 'Published'
        ? t(pagesLocaleVars.pageStatusPublished)
        : props.Page.status === 'Unpublished'
            ? t(pagesLocaleVars.pageStatusUnpublished)
            : props.Page.status === 'Draft'
                ? 'Draft'
                : props.Page.status;
});

const getTranslatedType = computed(() => {
    return props.Page.type === 'Landing'
        ? t(pagesLocaleVars.pageTypeLanding)
        : props.Page.type === 'Home'
            ? t(pagesLocaleVars.pageTypeHome)
            : props.Page.type === 'Product'
                ? t(pagesLocaleVars.pageTypeProduct)
                : props.Page.type === 'Collection'
                    ? t(pagesLocaleVars.pageTypeCollection)
                    : props.Page.type === 'List collection'
                        ? t(pagesLocaleVars.pageTypeListCollection)
                        : props.Page.type;
});

const getLastUpdated = computed(() => {
    return props.Page.Last_updated || (props.Page as any).updated_at;
});

const navigateToAnalytics = (event: Event) => {
    event.stopPropagation();
    router.push(`/analytic/detail/${props.Page.id}`);
};
</script>

<template>
    <IndexTableRow :id="String(Page.id)" :key="Number(Page.id)" :position="index" :selected="selected">
        <IndexTableCell :class="'cell__wrapper firt-cell'" @click="handleRowClick($event)">
            <BlockStack>
                <Box>
                    <Text variant="bodyMd" fontWeight="medium" as="span" class="clickable-text">{{ Page.title }}</Text>
                </Box>
                <Box>
                    <Text as="span" :tone="'subdued'" font-weight="regular" class="clickable-text">{{ Page.url }}</Text>
                </Box>
            </BlockStack>
        </IndexTableCell>
        <IndexTableCell :class="'cell__wrapper second-cell'" @click="handleRowClick($event)">
            <div class="badge__wrapper">
                <div :class="`badge__wrapper__${Page.status}`">
                    <Badge :progress="Page.status === 'Unpublished' ? 'incomplete' : 'complete'"
                        :tone="Page.status === 'Draft' ? 'info' : undefined" class="clickable-badge">
                        {{ getTranslatedStatus }}
                    </Badge>
                </div>
            </div>
        </IndexTableCell>
        <IndexTableCell :class="'cell__wrapper third-cell'" @click="handleRowClick($event)">
            <Text as="span" :alignment="'center'" numeric class="clickable-text">
                {{ getTranslatedType }}
            </Text>
        </IndexTableCell>
        <IndexTableCell :class="'cell__wrapper fourth-cell'" @click="handleRowClick($event)">
            <span class="clickable-text">
                {{ getLastUpdated }}
            </span>
        </IndexTableCell>
        <IndexTableCell :class="'cell__wrapper fifth-cell'" @click="handleIconClick">
            <Box>
                <InlineStack :gap="'500'">
                    <a @click="handleRowClick($event)">
                        <Icon :source="ComposeIcon"></Icon>
                    </a>
                    <a @click="handleRowClick($event)">
                        <Icon :source="ViewIcon"></Icon>
                    </a>
                    <a @click="navigateToAnalytics($event)">
                        <Icon :source="ChartPopularIcon"></Icon>
                    </a>
                </InlineStack>
            </Box>
        </IndexTableCell>
    </IndexTableRow>
</template>

<style scoped lang="scss">
.cell__wrapper {
    height: 40px;
}

.clickable-text {
    cursor: pointer;
    display: inline-block;
}

.clickable-badge {
    cursor: pointer;
}

.firt-cell {
    width: 45%;
}

.second-cell {
    width: 12%;
}

.third-cell {
    width: 10%;
}

.fourth-cell {
    width: 14%;
}

.fifth-cell {
    width: 15%;
}

.badge__wrapper {
    &__Published {
        .Polaris-Badge {
            background-color: var(--m-success-base-20);
            color: var(--m-success-base-30);

            svg {
                fill: var(--m-success-base-30);
            }
        }
    }

    &__unpublished {
        .Polaris-Badge {
            background-color: var(--m-overlay-color-2);
        }
    }
}
</style>
