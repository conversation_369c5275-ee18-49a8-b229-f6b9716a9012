import { computed } from 'vue';

import { usePagesStore } from '@/stores/pagesStore';

import type { AppliedFilter } from './types';

export function useFilters() {
    // Use the Pinia store
    const pagesStore = usePagesStore();

    const status = computed({
        get: () => pagesStore.status,
        set: (value) => pagesStore.setStatus(value),
    });

    const queryValue = computed({
        get: () => pagesStore.queryValue,
        set: (value) => pagesStore.setQueryValue(value),
    });

    const handleStatus = (value: string[]) => {
        pagesStore.setStatus(value);
    };

    const handleStatusRemove = () => {
        pagesStore.clearStatus();
    };

    const handleQueryValueRemove = () => {
        pagesStore.clearQueryValue();
    };

    const handleFiltersClearAll = () => {
        console.log('Clear all filters');
        pagesStore.clearAllFilters();
    };

    const appliedFilters = computed((): AppliedFilter[] => {
        const results: AppliedFilter[] = [];
        if (status.value && !isEmpty(status.value)) {
            console.log('Status', status.value);
            const name = 'status';
            results.push({
                name,
                label: disambiguateLabel(name, status.value),
                onRemove: handleStatusRemove,
            });
        }

        return results;
    });

    function disambiguateLabel(key: string, value: string | any[]): string {
        switch (key) {
            case 'status':
                return (value as string[]).map((val) => val).join(', ');
            default:
                return value as string;
        }
    }

    function isEmpty(value: string | string[]): boolean {
        if (Array.isArray(value)) {
            return value.length === 0;
        } else {
            return value === '' || value == null;
        }
    }

    return {
        status,
        queryValue,
        appliedFilters,
        handleStatus,
        handleStatusRemove,
        handleQueryValueRemove,
        handleFiltersClearAll,
    };
}
