<script setup lang="ts">
import { Badge, Card, Page } from '@ownego/polaris-vue';
import { usePagination } from '@utils/pagination';
import { ref, computed, onMounted, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter, useRoute } from 'vue-router';

import { formsLocaleVars } from '@/locales/forms';

import { SubmissionDatas, FormDatas, resourceName, SubmissionDataStatusOptions } from '../configs';
import { ListBlock } from './list-block';
import { EmptyStateSnippet } from './list-block/empty-state-snippet';
import { PaginateBlock } from './paginate-block';
import { SkeletonBlock } from './skeleton-block';
import { useFilters } from './utils';
const { t } = useI18n();
const router = useRouter();
const route = useRoute();

const isLoading = ref(true);
const formId = computed(() => route.params.id?.toString() || '');

onMounted(() => {
    setTimeout(() => {
        isLoading.value = false;
    }, 1000);
});

const selected = ref(0);
const { queryValue, status, handleStatus, appliedFilters } = useFilters();

const submissionData = ref(SubmissionDatas.filter((item) => (formId.value ? item.form_id === formId.value : true)));

const listBlockRef = ref();

const filteredData = computed(() => {
    let filtered = [...submissionData.value];

    if (formId.value) {
        filtered = filtered.filter((item) => item.form_id === formId.value);
    }

    const selectedOption = SubmissionDataStatusOptions[selected.value];
    if (selectedOption?.content !== 'All') {
        filtered = filtered.filter((submission) => submission.status === selectedOption.content);
    }

    if (queryValue.value) {
        filtered = filtered.filter(
            (submission) =>
                submission.first_name.toLowerCase().includes(queryValue.value.toLowerCase()) ||
                submission.last_name.toLowerCase().includes(queryValue.value.toLowerCase()) ||
                submission.gmail.toLowerCase().includes(queryValue.value.toLowerCase()),
        );
    }

    if (status.value.length > 0) {
        if (status.value.includes('All') && status.value.length === 1) return filtered;
        filtered = filtered.filter((submission) => status.value.includes(submission.status));
    }

    return filtered;
});

const { currentPage, paginatedData, totalPages, goToPreviousPage, goToNextPage } = usePagination(
    () => filteredData.value,
    3,
);

const handleFiltersSelect = (index: number) => {
    selected.value = index;
    currentPage.value = 1;
};

const handleFiltersQueryChange = (value: string) => {
    queryValue.value = value;
    currentPage.value = 1;
};

const updateStatus = (ids: string[], newStatus: string) => {
    submissionData.value = submissionData.value.map((submission) => {
        if (ids.includes(submission.id)) {
            return { ...submission, status: newStatus as 'Read' | 'Unread' };
        }
        return submission;
    });
};

const formData = computed(() => FormDatas.find((form) => form.form_id === formId.value));

watch(paginatedData, () => {
    listBlockRef.value?.clearSelection();
});

const getTranslatedStatusForm = computed(() => {
    if (!formData.value) return '';
    return formData.value.status === 'Published'
        ? t(formsLocaleVars.formsStatusPublished)
        : formData.value.status === 'Unpublished'
          ? t(formsLocaleVars.formsStatusUnpublished)
          : formData.value.status === 'Draft'
            ? 'Draft'
            : formData.value.status;
});

const translatedStatusOptions = computed(() => [
    {
        id: 'all',
        content: t(formsLocaleVars.submissionsStatusAll),
    },
    {
        id: 'read',
        content: t(formsLocaleVars.submissionsStatusRead),
    },
    {
        id: 'unread',
        content: t(formsLocaleVars.submissionsStatusUnread),
    },
]);

const tableHeadings = computed(() => [
    { title: t(formsLocaleVars.submissionsTableFormId) },
    { title: '' },
    { title: '' },
    { title: '' },
    { title: t(formsLocaleVars.submissionsTableStatus) },
    { title: t(formsLocaleVars.submissionsTableLastUpdated) },
]);
</script>

<template>
    <Page
        v-if="formData"
        :title="formData?.formtitle || 'Submissions'"
        :subtitle="formData?.form_id || ''"
        compactTitle
        v-bind="
            formData?.form_id
                ? {
                      backAction: {
                          content: t(formsLocaleVars.formsManagerTitle),
                          onAction: () => {
                              router.push('/forms');
                          },
                      },
                  }
                : {}
        "
    >
        <template #pageTitle>
            <span v-if="formData" class="badge-container">
                <Badge
                    class="status-badge"
                    :class="`status-badge--${formData.status.toLowerCase()}`"
                    :progress="formData.status === 'Unpublished' ? 'incomplete' : 'complete'"
                    :tone="formData.status === 'Draft' ? 'info' : undefined"
                >
                    {{ getTranslatedStatusForm }}
                </Badge>
            </span>
        </template>

        <SkeletonBlock v-if="isLoading" />

        <template v-else>
            <Card v-if="filteredData.length > 0" padding="none">
                <ListBlock
                    ref="listBlockRef"
                    :status="status"
                    :appliedFilters="appliedFilters"
                    :SubmissionDatas="paginatedData"
                    :resourceName="resourceName"
                    :tableHeadings="tableHeadings"
                    :SubmissionDataStatusOptions="translatedStatusOptions"
                    :selected="selected"
                    :handleFiltersSelect="handleFiltersSelect"
                    :update-status="updateStatus"
                    :handleFiltersQueryChange="handleFiltersQueryChange"
                    :handleStatus="handleStatus"
                />
                <PaginateBlock
                    :currentPage="currentPage"
                    :totalPages="totalPages"
                    :totalItems="filteredData.length"
                    :startItem="(currentPage - 1) * 3 + 1"
                    :endItem="Math.min(currentPage * 3, filteredData.length)"
                    :goToPreviousPage="goToPreviousPage"
                    :goToNextPage="goToNextPage"
                />
            </Card>

            <Card v-else padding="none">
                <EmptyStateSnippet />
            </Card>
        </template>
    </Page>
    <div v-else>
        <Card v-if="filteredData.length > 0" padding="none">
            <ListBlock
                ref="listBlockRef"
                :status="status"
                :appliedFilters="appliedFilters"
                :SubmissionDatas="paginatedData"
                :resourceName="resourceName"
                :tableHeadings="tableHeadings"
                :SubmissionDataStatusOptions="translatedStatusOptions"
                :selected="selected"
                :handleFiltersSelect="handleFiltersSelect"
                :update-status="updateStatus"
                :handleFiltersQueryChange="handleFiltersQueryChange"
                :handleStatus="handleStatus"
            />
            <PaginateBlock
                :currentPage="currentPage"
                :totalPages="totalPages"
                :totalItems="filteredData.length"
                :startItem="(currentPage - 1) * 3 + 1"
                :endItem="Math.min(currentPage * 3, filteredData.length)"
                :goToPreviousPage="goToPreviousPage"
                :goToNextPage="goToNextPage"
            />
        </Card>

        <Card v-else padding="none">
            <EmptyStateSnippet />
        </Card>
    </div>
</template>

<style scoped lang="scss">
.status-badge {
    &--published {
        background-color: var(--m-success-base-20);
        color: var(--m-success-base-30);

        svg {
            fill: var(--m-success-base-30);
        }
    }

    &--unpublished {
        background-color: var(--m-overlay-color-2);
    }
}
</style>
