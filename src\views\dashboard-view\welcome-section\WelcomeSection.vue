<script setup lang="ts">
import vars from '@locales/dashboard/vars';
import { Text } from '@ownego/polaris-vue';
import { useI18n } from 'vue-i18n';

import { CardBlock } from './card-block';
import { cardConfigs } from './configs';

const { t } = useI18n();
</script>

<template>
    <s-section padding="none" class="welcome">
        <div class="welcome__title">
            <Text :as="'h1'" :variant="'headingLg'">{{ t(vars.welcomeTitle) }}</Text>
            <Text :as="'p'" :variant="'bodyMd'">
                {{ t(vars.welcomeDescription) }}
            </Text>
        </div>
        <div class="welcome__content">
            <div v-for="card in cardConfigs" :key="card.titleKey">
                <CardBlock
                    :titleKey="card.titleKey"
                    :descriptionKey="card.descriptionKey"
                    :buttonColor="card.buttonColor"
                />
            </div>
        </div>
    </s-section>
</template>

<style lang="scss" scoped>
.welcome {
    &__title {
        display: flex;
        flex-direction: column;
        padding: 20px 16px 16px 16px;
        gap: 10px;
    }

    &__content {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        padding: 0px 16px 16px 16px;

        @media screen and (max-width: 768px) {
            display: block;
        }
    }
}
</style>
