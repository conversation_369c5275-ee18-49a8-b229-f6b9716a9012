import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

import type { Plan } from '@/views/settings-view/plans-section/types';

import { plans } from '@/views/settings-view/plans-section/configs';

import type { SlotLimits, SlotUsage } from './types';

const MIN_SLOTS = 5;
const MAX_SLOTS = 50;

const DEFAULT_LIMITS = {
    free: {
        slots: 140,
        forms: 2,
        submissions: 50,
        storage: 200,
    },
    'pay-as-you-go': {
        slots: 200,
        forms: 5,
        submissions: 500,
        storage: 500,
    },
    enterprise: {
        slots: Infinity,
        forms: Infinity,
        submissions: Infinity,
        storage: 102400, // 100GB (100 * 1024 MB)
    },
};

export const usePlanStore = defineStore('plan', () => {
    const billingType = ref<'monthly' | 'annual'>('monthly');
    const currentPlanId = ref('free');
    const showFeatureComparison = ref(false);
    const plansData = ref<Plan[]>(plans);

    const selectedSlots = ref(MIN_SLOTS);

    const slotLimits = ref<SlotLimits>({
        slots: DEFAULT_LIMITS.free.slots,
        forms: DEFAULT_LIMITS.free.forms,
        submissions: DEFAULT_LIMITS.free.submissions,
        storage: DEFAULT_LIMITS.free.storage,
    });

    const slotUsage = ref<SlotUsage>({
        pages: 0,
        sections: 0,
        forms: 0,
        submissions: 0,
        storage: 0,
        totalUsed: 0,
    });

    const setBillingType = (type: 'monthly' | 'annual') => {
        billingType.value = type;
    };

    const setCurrentPlan = (planId: string) => {
        currentPlanId.value = planId;

        if (planId === 'free') {
            slotLimits.value = { ...DEFAULT_LIMITS.free };
        } else if (planId === 'pay-as-you-go') {
            // Tính số slots tăng từ mức cơ bản
            const extraSlots = selectedSlots.value - MIN_SLOTS;
            const extraMultiplier = Math.floor(extraSlots / 5);

            // Cập nhật giới hạn với mức tăng
            slotLimits.value = {
                slots: DEFAULT_LIMITS['pay-as-you-go'].slots + selectedSlots.value,
                forms: DEFAULT_LIMITS['pay-as-you-go'].forms + extraMultiplier * 3,
                submissions: DEFAULT_LIMITS['pay-as-you-go'].submissions + extraMultiplier * 200,
                storage: DEFAULT_LIMITS['pay-as-you-go'].storage + extraMultiplier * 200,
            };
        } else if (planId === 'enterprise') {
            slotLimits.value = { ...DEFAULT_LIMITS.enterprise };
        }
    };

    const setSelectedSlots = (slots: number) => {
        // Lưu giá trị cũ để tính toán mức tăng
        const oldSelectedSlots = selectedSlots.value;

        // Cập nhật giá trị slots mới
        selectedSlots.value = Math.max(MIN_SLOTS, Math.min(MAX_SLOTS, slots));

        // Nếu đang ở gói pay-as-you-go
        if (currentPlanId.value === 'pay-as-you-go') {
            // Tính số slots extra mới và cũ (vượt quá MIN_SLOTS)
            const oldExtraSlots = oldSelectedSlots - MIN_SLOTS;
            const newExtraSlots = selectedSlots.value - MIN_SLOTS;

            // Tính số bậc 5-slots trước đây và hiện tại
            const oldMultiplier = Math.floor(oldExtraSlots / 5);
            const newMultiplier = Math.floor(newExtraSlots / 5);

            // Nếu có sự thay đổi về số bậc
            if (newMultiplier !== oldMultiplier) {
                // Tính toán lại các giới hạn từ mức cơ bản
                slotLimits.value = {
                    slots: DEFAULT_LIMITS['pay-as-you-go'].slots + selectedSlots.value,
                    forms: DEFAULT_LIMITS['pay-as-you-go'].forms + newMultiplier * 3,
                    submissions: DEFAULT_LIMITS['pay-as-you-go'].submissions + newMultiplier * 200,
                    storage: DEFAULT_LIMITS['pay-as-you-go'].storage + newMultiplier * 200,
                };
            } else {
                // Chỉ cập nhật số slots nếu không thay đổi bậc
                slotLimits.value.slots = DEFAULT_LIMITS['pay-as-you-go'].slots + selectedSlots.value;
            }
        }
    };

    const calculateExtraPrice = (slots: number) => {
        const extraSlots = Math.max(0, slots - MIN_SLOTS);
        return extraSlots * 2;
    };

    const toggleFeatureComparison = () => {
        showFeatureComparison.value = !showFeatureComparison.value;
    };

    const currentPlan = computed(() => {
        return plansData.value.find((plan) => plan.id === currentPlanId.value) || plansData.value[0];
    });

    const mappedPlans = computed(() =>
        plansData.value.map((plan) => ({
            title: plan.title,
            description: plan.description,
            type: plan.id,
            featuredText: plan.featuredText,
            specialLabel: plan.specialLabel,
            price: plan.prices,
            frequency: plan.prices.frequency[billingType.value],
            sale_price: plan.prices.discount && billingType.value === 'annual' ? plan.prices.discount : 0,
            features: plan.features,
            button: plan.buttonText
                ? {
                      content: plan.buttonText,
                      props: {
                          variant: 'primary',
                          onClick: () => setCurrentPlan(plan.id),
                      },
                  }
                : undefined,
            hasSlider: plan.hasSlider,
            publishedSlots: plan.id === 'pay-as-you-go' ? selectedSlots.value : plan.publishedSlots,
            hasUnlimitedSlots: plan.hasUnlimitedSlots,
        })),
    );

    const isSale = computed(() => billingType.value === 'annual');

    const totalSlotsUsed = computed(() => {
        return slotUsage.value.pages + slotUsage.value.sections;
    });

    const slotUsagePercentage = computed(() => {
        if (slotLimits.value.slots === Infinity) return 0;
        return (totalSlotsUsed.value / slotLimits.value.slots) * 100;
    });

    const formUsagePercentage = computed(() => {
        if (slotLimits.value.forms === Infinity) return 0;
        return (slotUsage.value.forms / slotLimits.value.forms) * 100;
    });

    const submissionUsagePercentage = computed(() => {
        if (slotLimits.value.submissions === Infinity) return 0;
        return (slotUsage.value.submissions / slotLimits.value.submissions) * 100;
    });

    const storageUsagePercentage = computed(() => {
        if (slotLimits.value.storage === Infinity) return 0;
        return (slotUsage.value.storage / slotLimits.value.storage) * 100;
    });

    const formAndSubmissionUsagePercentage = computed(() => {
        const formsPercentage = formUsagePercentage.value;
        const submissionsPercentage = submissionUsagePercentage.value;
        return formsPercentage * 0.6 + submissionsPercentage * 0.4;
    });

    const updateSlotUsage = (usage: Partial<SlotUsage>) => {
        slotUsage.value = { ...slotUsage.value, ...usage };
        slotUsage.value.totalUsed = slotUsage.value.pages + slotUsage.value.sections;
    };

    return {
        billingType,
        currentPlanId,
        showFeatureComparison,
        plansData,
        slotUsage,
        slotLimits,
        selectedSlots,
        MIN_SLOTS,
        MAX_SLOTS,

        setBillingType,
        setCurrentPlan,
        toggleFeatureComparison,
        updateSlotUsage,
        setSelectedSlots,
        calculateExtraPrice,

        currentPlan,
        mappedPlans,
        isSale,
        totalSlotsUsed,
        slotUsagePercentage,
        formUsagePercentage,
        submissionUsagePercentage,
        storageUsagePercentage,
        formAndSubmissionUsagePercentage,
    };
});
