<script setup lang="ts">
import { Page } from '@ownego/polaris-vue';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';

import { CmsHeader } from '@/components/cms-header';
import { formsLocaleVars } from '@/locales/forms';

import { FormsManagerSection } from './forms-manager-section';
import { SubmissionsManagerSection } from './submissions-manager-section';

const { t } = useI18n();

const formsTabs = [
    { id: 'Forms', content: t(formsLocaleVars.forms), panelID: 'forms-manager-section' },
    { id: 'Submissions', content: t(formsLocaleVars.submissions), panelID: 'submissions-manager-section' },
];

const activeTab = ref(0);

const handleTabChange = (tab: number) => {
    activeTab.value = tab;
};
</script>

<template>
    <Page :title="'Forms'" :primaryAction="{ content: 'Create form' }" v-if="activeTab === 0">
        <CmsHeader :tabs="formsTabs" :defaultActiveTab="0" @tab-change="handleTabChange" :isPage="true" />
        <div class="forms-container">
            <FormsManagerSection />
        </div>
    </Page>

    <Page :title="'Submissions'" v-if="activeTab === 1">
        <CmsHeader :tabs="formsTabs" :defaultActiveTab="1" @tab-change="handleTabChange" :isPage="true" />
        <div class="submissions-container">
            <SubmissionsManagerSection />
        </div>
    </Page>
</template>

<style scoped lang="scss">
.forms-container {
    margin-top: 16px;
}

.submissions-container {
    margin-top: 16px;
}
</style>
