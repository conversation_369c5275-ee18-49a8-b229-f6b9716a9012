<script setup lang="ts">
import { BlockStack, Box, Button, Text } from '@ownego/polaris-vue';
import { useI18n } from 'vue-i18n';

import { settingsLocaleVars } from '@/locales/settings';
import { useAppStore } from '@/stores/appStore';

const { t } = useI18n();
const appStore = useAppStore();

const openUninstallModal = () => {
    appStore.openModal('uninstall-default', 'Uninstall and handle data');
};
</script>

<template>
    <s-grid gridTemplateColumns="@container (inline-size <= 700px) 1fr, 2fr 5fr" gap="base">
        <s-box>
            <s-heading>{{ t(settingsLocaleVars.uninstallationTitle) }}</s-heading>
        </s-box>
        <s-section accessibilityLabel="App uninstallation section">
            <BlockStack :gap="'200'">
                <Text :as="'h3'" :variant="'headingSm'"> UNINSTALL AND HANDLE PAGEFLY DATA </Text>
                <Text :as="'p'" :variant="'bodyMd'">
                    To ensure your data's safety, we highly recommend uninstalling PageFly from this page instead of the
                    app listing. Learn more</Text
                >
                <Box>
                    <Button @click="openUninstallModal" :variant="'primary'" tone="critical">Uninstall</Button>
                </Box>
            </BlockStack>
        </s-section>
    </s-grid>
</template>

<style scoped lang="scss"></style>
