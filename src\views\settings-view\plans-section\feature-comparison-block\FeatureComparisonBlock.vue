<script setup lang="ts">
import { BlockStack, Box, Text, Icon, Card, List, ListItem } from '@ownego/polaris-vue';
import CheckIcon from '@shopify/polaris-icons/CheckIcon.svg';
import MinusIcon from '@shopify/polaris-icons/MinusIcon.svg';
import { useI18n } from 'vue-i18n';

import { settingsLocaleVars as vars } from '@/locales/settings';

import { planFeatures } from '../configs';
import { AccordionSnippet } from './accordion-snippet';

const { t } = useI18n();

const renderFeatureValue = (value: string | boolean) => {
    if (value === true) {
        return { icon: CheckIcon, text: '' };
    } else if (value === '—' || value === false) {
        return { icon: MinusIcon, text: '' };
    } else {
        return { icon: '', text: value };
    }
};
</script>

<template>
    <Card :padding="'none'">
        <Box :padding-inline="600" padding-block="400">
            <BlockStack>
                <div class="plan-headers">
                    <div class="plan-headers__empty"></div>
                    <div class="plan-headers__column">
                        <Text as="span" variant="bodyMd" font-weight="semibold" :alignment="'end'">
                            {{ t(vars.freePlan) }}
                        </Text>
                    </div>
                    <div class="plan-headers__column">
                        <Text as="span" variant="bodyMd" font-weight="semibold" :alignment="'end'">
                            {{ t(vars.payAsYouGoPlan) }}
                        </Text>
                    </div>
                    <div class="plan-headers__column">
                        <Text as="span" variant="bodyMd" font-weight="semibold" :alignment="'end'">
                            {{ t(vars.enterprisePlan) }}
                        </Text>
                    </div>
                </div>

                <div v-for="(category, categoryIndex) in planFeatures" :key="categoryIndex" class="feature-category">
                    <div class="category-header">
                        <Text as="h3" variant="headingSm" font-weight="semibold">{{ t(category.title) }}</Text>
                    </div>

                    <div class="feature-table">
                        <div
                            v-for="(feature, featureIndex) in category.features"
                            :key="featureIndex"
                            class="feature-row"
                        >
                            <div class="feature-name-column">
                                <Text as="span" variant="bodyMd">{{ t(feature.name) }}</Text>
                            </div>

                            <div class="plan-column">
                                <div v-if="renderFeatureValue(feature.free).icon" class="check-icon">
                                    <Icon :source="renderFeatureValue(feature.free).icon" />
                                </div>
                                <Text v-else as="span" variant="bodyMd">
                                    {{ t(String(feature.free)) }}
                                </Text>
                            </div>

                            <div class="plan-column">
                                <div v-if="renderFeatureValue(feature.payAsYouGo).icon" class="check-icon">
                                    <Icon :source="renderFeatureValue(feature.payAsYouGo).icon" />
                                </div>
                                <Text v-else as="span" variant="bodyMd">
                                    {{ t(String(feature.payAsYouGo)) }}
                                </Text>
                            </div>

                            <div class="plan-column">
                                <div v-if="renderFeatureValue(feature.enterprise).icon" class="check-icon">
                                    <Icon :source="renderFeatureValue(feature.enterprise).icon" />
                                </div>
                                <Text v-else as="span" variant="bodyMd">
                                    {{ t(String(feature.enterprise)) }}
                                </Text>
                            </div>
                        </div>
                    </div>
                </div>
            </BlockStack>
        </Box>
    </Card>
    <Card :padding="'400'">
        <div class="refund-policy">
            <Box>
                <Text as="span" variant="headingMd" font-weight="bold">{{ t(vars.refundPolicy) }}</Text>
            </Box>
            <Box>
                <Text class="refund-policy__title" as="span" variant="bodyMd" font-weight="medium">
                    {{ t(vars.refundPolicyTitle) }}
                </Text>
            </Box>
            <Box>
                <List type="bullet" :list-style-type="'none'" :gap="'extraTight'">
                    <ListItem>{{ t(vars.refundPolicyItem1) }}</ListItem>
                    <ListItem>{{ t(vars.refundPolicyItem2) }}</ListItem>
                    <ListItem>{{ t(vars.refundPolicyItem3) }}</ListItem>
                    <ListItem>{{ t(vars.refundPolicyItem4) }}</ListItem>
                </List>
            </Box>
        </div>
    </Card>
    <Box>
        <Card padding="400">
            <div class="faq-container">
                <Box>
                    <Text as="span" variant="headingMd" font-weight="bold">{{ t(vars.faq) }}</Text>
                </Box>
                <Box>
                    <AccordionSnippet />
                </Box>
            </div>
        </Card>
    </Box>
</template>

<style scoped lang="scss">
.feature-comparison-table {
    width: 100%;
    background-color: white;
    border-radius: 8px;
    border: 1px solid #e1e3e5;
    overflow: hidden;
}

.plan-headers {
    display: flex;
    height: 40px;
    align-items: center;

    &__empty {
        flex: 2;
    }

    &__column {
        flex: 1;
        display: flex;
        justify-content: end;
        align-items: center;
    }
}

.feature-table {
    width: 100%;
}

.category-header {
    padding: 10px 16px;
    background-color: var(--m-contrast-175);
    border-bottom: 1px solid #e1e3e5;
}

.feature-row {
    display: flex;
    height: 40px;
    gap: 10px;
    align-items: center;
    background-color: white;
    border-bottom: 1px solid #e1e3e5;
    padding-right: 10px;

    &:last-child {
        border-bottom: none;
    }
}

.feature-name-column {
    flex: 2;
    padding: 0 16px;
    height: 100%;
    display: flex;
    align-items: center;
    margin-right: 10px;
}

.plan-column {
    flex: 1;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: end;
}

.check-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.feature-category {
    &:not(:last-child) {
        margin-bottom: 0;
    }
}
.refund-policy {
    display: flex;
    flex-direction: column;
    gap: 10px;
    &__title {
        color: var(--m-black-text);
    }
}
.faq-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}
</style>
