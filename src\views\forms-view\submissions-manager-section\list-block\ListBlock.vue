<script setup lang="ts">
import { Divider, IndexFilters, IndexTable, useIndexResourceState, useSetIndexFiltersMode } from '@ownego/polaris-vue';
import { ref, computed, h, resolveComponent } from 'vue';
import { useI18n } from 'vue-i18n';

import { formsLocaleVars } from '@/locales/forms';

import { useFilters } from '../utils';
import { ListRowSnippet } from './list-row-snippet';

const { t } = useI18n();
const sortSelected = ref(['order asc']);
const { mode, setMode } = useSetIndexFiltersMode();

const props = defineProps<{
    SubmissionDatas: any[];
    resourceName: any;
    tableHeadings: any[];
    SubmissionDataStatusOptions: any[];
    selected: number;
    status: string[];
    handleFiltersSelect: (_value: any) => void;
    handleStatus: (_value: string[]) => void;
    handleFiltersQueryChange: (_value: any) => void;
    appliedFilters: any[];
    updateStatus: (_ids: string[], _newStatus: string) => void;
}>();

const { queryValue, handleQueryValueRemove, handleFiltersClearAll } = useFilters();
const currentSubmissionData = computed(() => props.SubmissionDatas);
const { selectedResources, allResourcesSelected, handleSelectionChange, clearSelection } =
    useIndexResourceState(currentSubmissionData);

defineExpose({
    clearSelection,
});
const filters = [
    {
        name: 'status',
        label: t(formsLocaleVars.submissionsStatusLabel),
        filter: () =>
            h(resolveComponent('ChoiceList'), {
                title: t(formsLocaleVars.submissionsStatusLabel),
                titleHidden: true,
                choices: [
                    { label: t(formsLocaleVars.submissionsStatusRead), value: 'Read' },
                    { label: t(formsLocaleVars.submissionsStatusUnread), value: 'Unread' },
                ],
                modelValue: props.status || [],
                onChange: props.handleStatus,
                allowMultiple: true,
            }),
        shortcut: true,
    },
];
const onHandleCancel = () => {};

const handleFiltersSort = (value: string[]) => {
    sortSelected.value = value;
};

const selectedSubmissions = computed(() =>
    props.SubmissionDatas.filter((s) => selectedResources.value.includes(String(s.id))),
);

const hasMixedStatus = computed(() => {
    const statuses = selectedSubmissions.value.map((s) => s.status);
    return new Set(statuses).size > 1;
});

const allAreRead = computed(
    () => selectedSubmissions.value.length > 0 && selectedSubmissions.value.every((s) => s.status === 'Read'),
);

const allAreUnread = computed(
    () => selectedSubmissions.value.length > 0 && selectedSubmissions.value.every((s) => s.status === 'Unread'),
);

const promotedBulkActions = computed(() => [
    {
        content: t(formsLocaleVars.submissionsStatusRead),
        onAction: () => props.updateStatus(selectedResources.value, 'Read'),
        disabled: allAreRead.value,
    },
    {
        content: t(formsLocaleVars.submissionsStatusUnread),
        onAction: () => props.updateStatus(selectedResources.value, 'Unread'),
        disabled: hasMixedStatus.value || allAreUnread.value,
    },
]);

const sortOptions: any[] = [
    {
        label: t(formsLocaleVars.formsTableFormTitle),
        value: 'order asc',
        directionLabel: t(formsLocaleVars.submissionsSortAscending),
    },
    {
        label: t(formsLocaleVars.formsTableFormTitle),
        value: 'order desc',
        directionLabel: t(formsLocaleVars.submissionsSortDescending),
    },
    {
        label: t(formsLocaleVars.submissionsTableLastUpdated),
        value: 'customer asc',
        directionLabel: t(formsLocaleVars.submissionsSortAscending),
    },
    {
        label: t(formsLocaleVars.submissionsTableLastUpdated),
        value: 'customer desc',
        directionLabel: t(formsLocaleVars.submissionsSortDescending),
    },
];
</script>

<template>
    <IndexFilters
        :sortOptions="sortOptions"
        :sortSelected="sortSelected"
        :queryValue="queryValue"
        :queryPlaceholder="t(formsLocaleVars.submissionsSearchingIn)"
        :cancelAction="{ onAction: onHandleCancel, disabled: false, loading: false }"
        :tabs="SubmissionDataStatusOptions"
        :selected="selected"
        :filters="filters"
        :appliedFilters="appliedFilters"
        :mode="mode"
        @set-mode="setMode"
        @query-change="handleFiltersQueryChange"
        @query-clear="handleQueryValueRemove"
        @sort="handleFiltersSort"
        @select="handleFiltersSelect"
        @clear-all="handleFiltersClearAll"
        :can-create-new-view="false"
    />

    <IndexTable
        :condensed="false"
        :resourceName="resourceName"
        :itemCount="SubmissionDatas.length"
        :selectedItemsCount="allResourcesSelected ? 'All' : selectedResources.length"
        @selection-change="handleSelectionChange"
        :headings="tableHeadings"
        :promoted-bulk-actions="promotedBulkActions"
    >
        <ListRowSnippet
            v-for="SubmissionData in props.SubmissionDatas"
            :key="SubmissionData.id"
            :SubmissionData="SubmissionData"
            :index="Number(SubmissionData.id)"
            :selected="selectedResources.includes(String(SubmissionData.id))"
        />
    </IndexTable>

    <Divider borderColor="border" />
</template>

<style scoped lang="scss"></style>
