# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "4fc1aca2b1a9b6bfa39c8ea7638f542c"
application_url = "https://shopify.dev/apps/default-app-home"
embedded = true
name = "Landing Page Builder Stag"
handle = "landing-page-builder-stag"

[build]
include_config_on_deploy = true

[webhooks]
api_version = "2025-04"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_files,read_products,read_themes,write_files,write_products,write_themes"

[auth]
redirect_urls = [ "https://shopify.dev/apps/default-app-home/api/auth" ]

[pos]
embedded = false
