<script setup lang="ts">
import { IndexTableRow, IndexTableCell, Text, Badge } from '@ownego/polaris-vue';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

import vars from '@/locales/forms/vars';
import router from '@/router';

import type { ListRowSnippetProps } from './types';

const { t } = useI18n();
const props = defineProps<ListRowSnippetProps>();

const handleRowClick = (event: Event) => {
    event.stopPropagation();
    router.push(`/submissions/${props.SubmissionData.id}/detail`);
};

const getTranslatedStatus = computed(() => {
    return props.SubmissionData.status === 'Read'
        ? t(vars.submissionsStatusRead)
        : props.SubmissionData.status === 'Unread'
          ? t(vars.submissionsStatusUnread)
          : props.SubmissionData.status;
});
</script>

<template>
    <IndexTableRow
        :id="String(SubmissionData.id)"
        :key="Number(SubmissionData.id)"
        :position="index"
        :selected="selected"
    >
        <IndexTableCell class="cell__wrapper firt-cell" @click="handleRowClick($event)">
            <Text variant="bodyMd" fontWeight="medium" as="span" class="clickable-text">{{
                SubmissionData.form_id
            }}</Text>
        </IndexTableCell>

        <IndexTableCell class="cell__wrapper second-cell" @click="handleRowClick($event)">
            <Text as="span" variant="headingSm" font-weight="medium" class="clickable-text">{{
                SubmissionData.gmail
            }}</Text>
        </IndexTableCell>

        <IndexTableCell class="cell__wrapper third-cell" @click="handleRowClick($event)">
            <Text as="span" variant="headingSm" font-weight="medium" class="clickable-text">{{
                SubmissionData.first_name
            }}</Text>
        </IndexTableCell>

        <IndexTableCell class="cell__wrapper fourth-cell" @click="handleRowClick($event)">
            <Text as="span" variant="headingSm" font-weight="medium" class="clickable-text">{{
                SubmissionData.last_name
            }}</Text>
        </IndexTableCell>

        <IndexTableCell class="cell__wrapper fifth-cell" @click="handleRowClick($event)">
            <div class="badge__wrapper">
                <div :class="`badge__wrapper__${SubmissionData.status}`">
                    <Badge :progress="SubmissionData.status === 'Read' ? 'incomplete' : 'complete'">
                        {{ getTranslatedStatus }}
                    </Badge>
                </div>
            </div>
        </IndexTableCell>

        <IndexTableCell class="cell__wrapper sixth-cell" @click="handleRowClick($event)">
            <Text as="span" alignment="center" class="clickable-text">{{ SubmissionData.Last_updated }}</Text>
        </IndexTableCell>
    </IndexTableRow>
</template>

<style scoped lang="scss">
.cell__wrapper {
    height: 52px;
}

.clickable-text {
    cursor: pointer;
    display: inline-block;
}

.firt-cell {
    width: 14%;
}

.second-cell {
    width: 19%;
}

.third-cell {
    width: 19%;
}

.fourth-cell {
    width: 18%;
}

.fifth-cell {
    width: 12%;
}

.sixth-cell {
    width: 14%;
}

.badge__wrapper {
    cursor: pointer;

    &__Read {
        .Polaris-Badge {
            background-color: var(--m-blue-base-20);
            color: var(--m-success-base-30);

            svg {
                fill: var(--m-success-base-30);
            }
        }
    }

    &__Unread {
        .Polaris-Badge {
            background-color: var(--m-overlay-color-2);
        }
    }
}
</style>
