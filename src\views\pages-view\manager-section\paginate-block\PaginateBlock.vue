<script setup lang="ts">
import Info from '@assets/svgs/info.svg';
import { Box, InlineStack, Pagination } from '@ownego/polaris-vue';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

import { pagesLocaleVars } from '@/locales/pages';

const { t } = useI18n();
defineProps<{
    currentPage: number;
    totalPages: number;
    goToPreviousPage: () => void;
    goToNextPage: () => void;
}>();

const learnMoreText = computed(() => {
    return t(pagesLocaleVars.pagesLearnMore);
});

const pagesText = computed(() => {
    return t(pagesLocaleVars.pageManagerTitle);
});
</script>

<template>
    <Box :padding="300">
        <InlineStack :align="'space-between'" style="width: 100%">
            <div>
                <Pagination
                    :hasPrevious="currentPage > 1"
                    :hasNext="currentPage < totalPages"
                    @previous="goToPreviousPage"
                    @next="goToNextPage"
                    :previousTooltip="t(pagesLocaleVars.paginationPrevious)"
                    :nextTooltip="t(pagesLocaleVars.paginationNext)"
                >
                    {{ currentPage }} {{ t(pagesLocaleVars.paginationOf) }} {{ totalPages }}
                </Pagination>
            </div>

            <Box :padding="100">
                <div class="learn-more">
                    <InlineStack :align="'center'" :gap="150">
                        <Info />
                        <Box>
                            {{ learnMoreText }} <a href="#" target="_blank">{{ pagesText.toLowerCase() }}</a>
                        </Box>
                    </InlineStack>
                </div>
            </Box>
        </InlineStack>
    </Box>
</template>

<style scoped lang="scss">
.learn-more {
    a {
        color: var(--m-blue-base);
        text-decoration: none;

        &:active {
            color: var(--m-blue-base);
        }
    }
}

.items-text {
    margin-top: 0.5rem;
    margin-left: 0.5rem;
}
</style>
