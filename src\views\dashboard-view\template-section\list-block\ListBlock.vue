<script setup lang="ts">
import { dashboardLocaleVars } from '@locales/dashboard';
import { Box, InlineGrid, Text } from '@ownego/polaris-vue';
import { ref, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';

import { templateList, type TemplateItem } from './configs';
import { ListItemSnippet } from './list-item-snippet';
import { ListSkeletonSnippet } from './list-skeleton-snippet';

const { t } = useI18n();

const props = defineProps<{
    filterTab: string;
    searchQuery: string;
    initialLoading?: boolean;
}>();

const emit = defineEmits<{
    'loading-change': [isLoading: boolean];
}>();

const templates = ref<TemplateItem[]>([...templateList]);
const selectedTemplate = ref<TemplateItem | null>(null);
const isLoading = ref(false);

watch(
    () => props.initialLoading,
    (newValue) => {
        if (newValue !== undefined) {
            isLoading.value = newValue;
            emit('loading-change', newValue);
        }
    },
    { immediate: true },
);

watch(
    isLoading,
    (newValue) => {
        emit('loading-change', newValue);
    },
    { immediate: true },
);

watch(
    () => props.filterTab,
    (newTab, oldTab) => {
        if (newTab !== oldTab) {
            clearSelections();
        }
    },
    { immediate: false },
);

watch([() => props.filterTab, () => props.searchQuery], () => {
    isLoading.value = true;
    setTimeout(() => {
        isLoading.value = false;
    }, 800);
});

const filteredTemplates = computed(() => {
    let result = [...templates.value];

    if (props.filterTab !== 'all') {
        result = result.filter((template) => template.category === props.filterTab);
    }

    if (props.searchQuery) {
        const query = props.searchQuery.toLowerCase();
        result = result.filter((template) => template.name.toLowerCase().includes(query));
    }

    return result;
});

const totalLoading = computed(() => {
    return isLoading.value;
});

const clearSelections = () => {
    templates.value = templates.value.map((template) => ({
        ...template,
        selected: false,
    }));

    selectedTemplate.value = null;
};

const handleTemplateSelect = (item: TemplateItem) => {
    templates.value = templates.value.map((template) => ({
        ...template,
        selected: template.id === item.id,
    }));

    selectedTemplate.value = item;
};

const getSelectedTemplate = () => {
    return selectedTemplate.value;
};

const getLoadingState = () => {
    return totalLoading.value;
};

defineExpose({
    getSelectedTemplate,
    clearSelections,
    getLoadingState,
});
</script>

<template>
    <Box paddingInline="400" paddingBlockEnd="400">
        <div class="list-container">
            <ListSkeletonSnippet v-if="totalLoading" />
            <InlineGrid :columns="3" :gap="400" v-else>
                <ListItemSnippet
                    v-for="item in filteredTemplates"
                    :key="item.id"
                    :item="item"
                    @select="handleTemplateSelect"
                />
            </InlineGrid>
            <div v-if="!totalLoading && filteredTemplates.length === 0" class="empty-state">
                <Text :as="'p'" :variant="'bodyLg'" :alignment="'center'" :tone="'subdued'">
                    {{ t(dashboardLocaleVars.noTemplatesFound) }}
                </Text>
            </div>
        </div>
    </Box>
</template>

<style scoped lang="scss">
.list-container {
    position: relative;
    min-height: 450px;
    overflow: hidden;
}

.empty-state {
    padding: 40px;
    text-align: center;
}
</style>
