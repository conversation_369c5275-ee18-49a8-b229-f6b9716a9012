    export interface ChartItem {
    date: string;
    value: number;
}
export interface PageItem {
    id: string;
    title: string;
    status: string;
    type: string;
    url: string;
    updated_at: string;
    created_at: string;
    analyticId?: {
        id: string;
        pageId: string;
        addToCartRate: number;
        productViewsRate: number;
        visitors: number;
        sessions: number;
        items: ChartItem        [];
        graphData: {
            sales: { date: string; value: number }[];
            comparison?: { date: string; value: number }[];
        };
        created_at: string;
        updated_at: string;
    }
}


export interface PagesStoreState {
    pages: PageItem[];
    isLoading: boolean;
    queryValue: string;
    status: string[];
    currentPage: number;
    itemsPerPage: number;
    activeTab: string;
}
