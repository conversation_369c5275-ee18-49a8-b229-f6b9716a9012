import { ref, computed } from 'vue';

import type { AppliedFilter } from '../types';
export function useFilters() {
    const status = ref<string[]>([]);
    const queryValue = ref('');

    const handleStatus = (value: string[]) => {
        status.value = value;
    };

    const handleStatusRemove = () => {
        status.value = [];
    };

    const handleQueryValueRemove = () => {
        queryValue.value = '';
    };

    const handleFiltersClearAll = () => {
        console.log('Clear all filters');
        handleStatusRemove();
        handleQueryValueRemove();
    };

    const appliedFilters = computed((): AppliedFilter[] => {
        const results: AppliedFilter[] = [];
        if (status.value && !isEmpty(status.value)) {
            console.log('Status', status.value);
            const name = 'status';
            results.push({
                name,
                label: disambiguateLabel(name, status.value),
                onRemove: handleStatusRemove,
            });
        }

        return results;
    });

    function disambiguateLabel(key: string, value: string | any[]): string {
        switch (key) {
            case 'status':
                return (value as string[]).map((val) => val).join(', ');
            default:
                return value as string;
        }
    }

    function isEmpty(value: string | string[]): boolean {
        if (Array.isArray(value)) {
            return value.length === 0;
        } else {
            return value === '' || value == null;
        }
    }

    return {
        status,
        queryValue,
        appliedFilters,
        handleStatus,
        handleStatusRemove,
        handleQueryValueRemove,
        handleFiltersClearAll,
    };
}
