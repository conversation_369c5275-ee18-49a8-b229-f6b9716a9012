import vars from './vars';

export const formsLocaleEN = {
    [vars.forms]: 'Forms',
    [vars.submissions]: 'Submissions',

    [vars.formsManagerTitle]: 'Forms Manager',
    [vars.formsStatusLabel]: 'Status',
    [vars.formsStatusAll]: 'All',
    [vars.formsStatusPublished]: 'Published',
    [vars.formsStatusUnpublished]: 'Unpublished',

    [vars.formsTableFormId]: 'Form ID',
    [vars.formsTableFormTitle]: 'Form Title',
    [vars.formsTableStatus]: 'Status',
    [vars.formsTableSubmissions]: 'Submissions',
    [vars.formsTableLastUpdated]: 'Last updated',

    [vars.formsActionsPublish]: 'Publish',
    [vars.formsActionsUnpublish]: 'Unpublish',
    [vars.formsActionsEdit]: 'Edit',
    [vars.formsActionsView]: 'View',
    [vars.formsActionsDelete]: 'Delete',
    [vars.formsActionsDuplicate]: 'Duplicate',

    [vars.formsEmptyTitle]: 'No forms found',
    [vars.formsEmptyMessage]: 'Create your first form',
    [vars.formsEmptyCreateNew]: 'Create new form',

    [vars.formsSearchPlaceholder]: 'Search forms',

    [vars.submissionsTitle]: 'Submissions',
    [vars.submissionsStatusLabel]: 'Status',
    [vars.submissionsStatusAll]: 'All',
    [vars.submissionsStatusRead]: 'Read',
    [vars.submissionsStatusUnread]: 'Unread',
    [vars.submissionsLearnMore]: 'Learn more about',
    [vars.submissionsEmptyTitle]: 'No submissions found',
    [vars.submissionsEmptyMessage]: 'form is not have submissions',
    [vars.submissionsGoBack]: 'Go back',
    [vars.submissionsSearchingIn]: 'Searching in all',
    [vars.submissionsSortAscending]: 'Ascending',
    [vars.submissionsSortDescending]: 'Descending',

    [vars.submissionsTableFormId]: 'Form ID',
    [vars.submissionsTableEmail]: 'Email',
    [vars.submissionsTableName]: 'Name',
    [vars.submissionsTableStatus]: 'Status',
    [vars.submissionsTableLastUpdated]: 'Last updated',

    [vars.paginationPrevious]: 'Previous',
    [vars.paginationNext]: 'Next',
    [vars.paginationOf]: 'of',
};
