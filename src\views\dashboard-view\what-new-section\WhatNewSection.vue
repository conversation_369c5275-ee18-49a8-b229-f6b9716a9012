<script setup lang="ts">
import { dashboardLocaleVars } from '@locales/dashboard';
import { InlineStack, Pagination, Text } from '@ownego/polaris-vue';
import emblaCarouselVue from 'embla-carousel-vue';
import { ref, onMounted, onUnmounted } from 'vue';
import { useI18n } from 'vue-i18n';

import { sliderItems } from './configs';
import { SliderItemBlock } from './slider-item-block';

const { t } = useI18n();
const canScrollPrev = ref(false);
const autoPlayInterval = ref<number | null>(null);
const autoPlayDelay = 5000;

const [emblaRef, emblaApi] = emblaCarouselVue({
    loop: true,
    align: 'start',
    slidesToScroll: 1,
    dragFree: false,
    containScroll: 'trimSnaps',
    inViewThreshold: 0,
    skipSnaps: false,
    startIndex: 0,
});

const scrollPrev = () => {
    if (emblaApi.value && canScrollPrev.value) emblaApi.value.scrollPrev();
};

const scrollNext = () => {
    if (emblaApi.value) emblaApi.value.scrollNext();
};

const updateButtonState = () => {
    if (!emblaApi.value) return;
    canScrollPrev.value = emblaApi.value.canScrollPrev() && emblaApi.value.selectedScrollSnap() !== 0;
};

const startAutoPlay = () => {
    stopAutoPlay();
    autoPlayInterval.value = window.setInterval(() => {
        if (emblaApi.value) {
            emblaApi.value.scrollNext();
        }
    }, autoPlayDelay);
};

const stopAutoPlay = () => {
    if (autoPlayInterval.value !== null) {
        clearInterval(autoPlayInterval.value);
        autoPlayInterval.value = null;
    }
};

onMounted(() => {
    if (emblaApi.value) {
        emblaApi.value.on('select', updateButtonState);
        emblaApi.value.on('init', updateButtonState);
        startAutoPlay();
    }
});

onUnmounted(() => {
    if (emblaApi.value) {
        emblaApi.value.off('select', updateButtonState);
    }
    stopAutoPlay();
});
</script>

<template>
    <s-section>
        <div class="slider">
            <InlineStack :align="'space-between'">
                <Text :as="'h2'" :variant="'headingMd'">{{ t(dashboardLocaleVars.sliderTitle) }}</Text>
                <a class="slider__see-all" href="">{{ t(dashboardLocaleVars.sliderSeeAll) }}</a>
            </InlineStack>

            <div class="slider__embla" ref="emblaRef">
                <div class="slider__container">
                    <div v-for="i in sliderItems" :key="i.id" class="slider__slide">
                        <SliderItemBlock :sliderItem="i" />
                    </div>
                </div>
            </div>

            <Pagination
                :hasPrevious="canScrollPrev"
                hasNext
                @previous="scrollPrev"
                @next="scrollNext"
                @mouseenter="stopAutoPlay"
                @mouseleave="startAutoPlay"
            />
        </div>
    </s-section>
</template>

<style scoped lang="scss">
.slider {
    display: flex;
    flex-direction: column;
    gap: 10px;

    &__embla {
        overflow: hidden;
        padding-right: 16px;
    }

    &__see-all {
        text-decoration: none;
    }

    &__container {
        display: flex;
    }

    &__slide {
        flex: 0 0 670px;
        min-width: 0;
        transition: opacity 0.3s ease;
        padding-right: 16px;
        box-sizing: border-box;
    }

    a {
        color: var(--m-blue-base);
        text-decoration: none;

        &.active {
            color: var(--m-blue-base);
        }
    }
}
</style>
