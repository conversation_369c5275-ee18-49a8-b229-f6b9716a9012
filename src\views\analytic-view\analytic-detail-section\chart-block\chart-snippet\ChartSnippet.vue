<script setup lang="ts">
import { scaleTime, scaleLinear, max, line as d3Line, curveNatural as d3CurveNatural, bisector } from 'd3';
import { computed, ref } from 'vue';

import type { DataPoint } from '@/views/analytic-view/analytic-detail-section/chart-block/types';

import { useAnalyticStore } from '@/stores/analyticStore';

const props = defineProps<{
    showComparison?: boolean;
}>();

const analyticStore = useAnalyticStore();
const activeItem = computed(() => analyticStore.activeItemData);
const data = computed(() => analyticStore.formattedSalesData);
const comparisonData = computed(() => analyticStore.formattedComparisonData);
const hoveredPoint = ref<DataPoint | null>(null);

const xScale = computed(() => {
    if (data.value.length === 0) return scaleTime().domain([new Date(), new Date()]).range([0, 100]);
    return scaleTime()
        .domain([data.value[0].date, data.value[data.value.length - 1].date])
        .range([0, 100]);
});

const yScale = computed(() => {
    return scaleLinear()
        .domain([0, max(data.value.map((d) => Number(d.value))) ?? 0])
        .range([100, 0]);
});

const line = computed(() => {
    return d3Line<DataPoint>()
        .x((d) => xScale.value(d.date))
        .y((d) => yScale.value(d.value))
        .curve(d3CurveNatural);
});

const comparisonLine = computed(() => {
    if (!comparisonData.value) return '';
    return d3Line<DataPoint>()
        .x((d) => xScale.value(d.date))
        .y((d) => yScale.value(d.value))
        .curve(d3CurveNatural)(comparisonData.value) || '';
});

const dPath = computed(() => {
    if (data.value.length === 0) return '';
    const result = line.value(data.value);
    return result ?? '';
});

const maxValue = computed(() => {
    if (data.value.length === 0) return 0;
    return Math.max(...data.value.map((d) => Number(d.value)));
});

const filteredData = computed(() => {
    if (data.value.length === 0) return [];
    return data.value
        .map((day, i) => ({ ...day, originalIndex: i }))
        .filter(
            (d) => d.originalIndex === 0 || d.originalIndex === data.value.length - 1 || d.value === maxValue.value,
        );
});

const bisectDate = bisector<DataPoint, Date>((d) => d.date).left;

const handleMouseMove = (event: MouseEvent) => {
    if (data.value.length === 0) return;

    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
    const x = event.clientX - rect.left;
    const xPercent = (x / rect.width) * 100;

    const xDate = xScale.value.invert(xPercent);

    const index = bisectDate(data.value, xDate, 1);
    const d0 = data.value[index - 1];
    const d1 = data.value[index];

    if (d0 && d1) {
        const closestPoint = xDate.getTime() - d0.date.getTime() > d1.date.getTime() - xDate.getTime() ? d1 : d0;
        hoveredPoint.value = closestPoint;
    } else if (d0) {
        hoveredPoint.value = d0;
    } else if (d1) {
        hoveredPoint.value = d1;
    }
};

const handleMouseLeave = () => {
    hoveredPoint.value = null;
};

const colorPairs = [
    { start: '#8b5cf6', end: '#f472b6' },
    { start: '#3b82f6', end: '#0ea5e9' },
    { start: '#10b981', end: '#34d399' },
    { start: '#f59e0b', end: '#fbbf24' },
    { start: '#ef4444', end: '#f87171' },
];

const getItemColorIndex = () => {
    if (!activeItem.value) return 0;
    const allItems = analyticStore.items;
    const index = allItems.findIndex((item) => item.key === activeItem.value?.key);
    return index >= 0 ? index % colorPairs.length : 0;
};

const getStrokeColor = () => {
    return `url(#linePulse-gradient-${getItemColorIndex()})`;
};

const getHoverDotColor = () => {
    return colorPairs[getItemColorIndex()].start;
};

const getPulseDotColor = () => {
    return colorPairs[getItemColorIndex()].end;
};

const formatValue = (value: number) => {
    if (activeItem.value && activeItem.value.unit) {
        return value.toLocaleString() + ' ' + activeItem.value.unit;
    }
    return value.toLocaleString();
};
</script>

<template>
    <div class="chart-block">
        <div class="chart-container">
            <div class="y-axis-labels">
                <div v-for="(value, i) in yScale.ticks(8).map(yScale.tickFormat(8, 'd'))" :key="i" class="tick"
                    :style="{ top: `${yScale(+value)}%` }">
                    {{ value }}
                </div>
            </div>

            <div class="chart-area" @mousemove="handleMouseMove" @mouseleave="handleMouseLeave">
                <div v-if="hoveredPoint" class="hover-line" :style="{
                    left: `${xScale(hoveredPoint.date)}%`,
                }" />

                <div v-if="hoveredPoint" class="tooltip" :style="{
                    left: `${xScale(hoveredPoint.date)}%`,
                    top: `${yScale(hoveredPoint.value)}%`,
                }">
                    <div class="tooltip-content">
                        <div class="tooltip-date">
                            {{ hoveredPoint.date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }) }}
                        </div>
                        <div class="tooltip-value">
                            {{ formatValue(hoveredPoint.value) }}
                        </div>
                        <div v-if="activeItem" class="tooltip-title">
                            {{ activeItem.label }}
                        </div>
                    </div>
                </div>

                <div v-if="hoveredPoint" class="hover-dot" :style="{
                    left: `${xScale(hoveredPoint.date)}%`,
                    top: `${yScale(hoveredPoint.value)}%`,
                    backgroundColor: getHoverDotColor(),
                }" />

                <div v-if="data.length > 0" class="pulse-dot" :style="{
                    left: `${xScale(data[data.length - 1].date)}%`,
                    top: `${yScale(data[data.length - 1].value)}%`,
                    backgroundColor: getPulseDotColor(),
                }">
                    <div class="ping" :style="{ backgroundColor: getPulseDotColor() }"></div>
                </div>

                <svg viewBox="0 0 100 100" class="svg-chart" preserveAspectRatio="none">
                    <g v-for="(tick, i) in yScale.ticks(8).map(yScale.tickFormat(8, 'd'))" :key="i"
                        :transform="`translate(0,${yScale(+tick)})`" class="grid-line">
                        <line x1="0" x2="100" stroke="currentColor" stroke-dasharray="6,5" stroke-width="0.5"
                            vector-effect="non-scaling-stroke" />
                    </g>

                    <!-- Main data line -->
                    <path :d="dPath" fill="none" :stroke="getStrokeColor()" stroke-width="2"
                        vector-effect="non-scaling-stroke" />

                    <!-- Comparison line (dotted) -->
                    <path v-if="props.showComparison && comparisonLine" :d="comparisonLine" fill="none" stroke="#9ca3af"
                        stroke-width="2" stroke-dasharray="4,4" vector-effect="non-scaling-stroke" />

                    <defs>
                        <linearGradient v-for="(color, i) in colorPairs" :key="i" :id="`linePulse-gradient-${i}`"
                            x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" :stop-color="color.start" />
                            <stop offset="100%" :stop-color="color.end" />
                        </linearGradient>
                    </defs>
                </svg>

                <div class="mouse-overlay" />

                <div class="x-axis-labels">
                    <div v-for="(day, i) in filteredData" :key="i" class="x-label" :style="{
                        left: `${xScale(day.date)}%`,
                        transform: `translateX(${day.originalIndex === 0
                            ? '0%'
                            : day.originalIndex === data.length - 1
                                ? '-100%'
                                : '-50%'
                            })`,
                    }">
                        {{ day.date.toLocaleDateString('en-US', { month: 'numeric', day: 'numeric' }) }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.chart-block {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.chart-header {
    padding: 0 1.5rem;
}

.chart-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #374151;
    margin: 0;
}

.chart-container {
    position: relative;
    height: 18rem; // h-72
    width: 100%;
    --marginTop: 0px;
    --marginRight: 8px;
    --marginBottom: 25px;
    --marginLeft: 25px;
    user-select: none;
}

.y-axis-labels {
    position: absolute;
    inset: 0;
    height: calc(100% - var(--marginTop) - var(--marginBottom));
    width: var(--marginLeft);
    transform: translateY(var(--marginTop));
    overflow: visible;

    .tick {
        position: absolute;
        font-size: 0.75rem; // text-xs
        font-variant-numeric: tabular-nums;
        color: #6b7280; // text-gray-500
        transform: translateY(-50%);
        text-align: right;
        width: 100%;
        padding-right: 0.5rem; // pr-2
    }
}

.chart-area {
    position: absolute;
    inset: 0;
    height: calc(100% - var(--marginTop) - var(--marginBottom));
    width: calc(100% - var(--marginLeft) - var(--marginRight));
    transform: translate(var(--marginLeft), var(--marginTop));
    overflow: visible;
    cursor: crosshair;
}

.mouse-overlay {
    position: absolute;
    inset: 0;
    z-index: 10;
}

.hover-line {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 1px;
    background-color: #6b7280; // gray-500
    opacity: 0.5;
    pointer-events: none;
    z-index: 5;
}

.hover-dot {
    position: absolute;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #8b5cf6; // purple-500
    transform: translate(-50%, -50%);
    pointer-events: none;
    z-index: 20;
}

.pulse-dot {
    position: absolute;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #ec4899; // pink-500
    transform: translate(-50%, -50%);
    pointer-events: none;
    z-index: 15;

    .ping {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background-color: #ec4899; // pink-500
        transform: translate(-50%, -50%);
        opacity: 0.8;
        animation: ping 1.5s cubic-bezier(0, 0, 0.2, 1) infinite;
    }
}

@keyframes ping {

    75%,
    100% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 0;
    }
}

.tooltip {
    position: absolute;
    transform: translate(-50%, -100%);
    margin-top: -8px;
    pointer-events: none;
    z-index: 30;

    .tooltip-content {
        background-color: #1f2937; // gray-800
        color: white;
        border-radius: 0.375rem; // rounded-md
        padding: 0.5rem 0.75rem; // p-2 px-3
        box-shadow:
            0 4px 6px -1px rgba(0, 0, 0, 0.1),
            0 2px 4px -1px rgba(0, 0, 0, 0.06); // shadow-md
        text-align: center;
        min-width: 80px;
    }

    .tooltip-date {
        font-size: 0.75rem; // text-xs
        opacity: 0.7;
    }

    .tooltip-value {
        font-weight: 600; // font-semibold
        font-size: 0.875rem; // text-sm
    }

    .tooltip-title {
        font-size: 0.75rem; // text-xs
        color: var(--p-color-text-success);
        margin-top: 2px;
    }
}

.x-axis-labels {
    position: absolute;
    bottom: -25px;
    left: 0;
    right: 0;

    .x-label {
        position: absolute;
        font-size: 0.75rem; // text-xs
        color: #6b7280; // text-gray-500
        text-align: center;
    }
}

.svg-chart {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: visible;
}

.grid-line {
    color: #e5e7eb; // gray-200
}
</style>
