import type { Pages, FilterChoice } from './types';

export const Page: Pages[] = [
    {
        id: '1020',
        title: 'Regular page 1',
        url: 'Not available',
        status: 'Unpublished',
        type: 'Landing',
        Last_updated: 'Nov 26, 2024',
    },
    {
        id: '1019',
        title: 'Home page 1',
        url: '/',
        status: 'Published',
        type: 'Home',
        Last_updated: 'Nov 26, 2024',
    },
    {
        id: '1018',
        title: 'Product page 1',
        url: '/product/1',
        status: 'Published',
        type: 'Product',
        Last_updated: 'Nov 26, 2024',
    },
    {
        id: '1021',
        title: 'Regular page 2',
        url: 'Not available',
        status: 'Unpublished',
        type: 'Landing',
        Last_updated: 'Nov 27, 2024',
    },
    {
        id: '1022',
        title: 'Home page 2',
        url: '/home-2',
        status: 'Published',
        type: 'Home',
        Last_updated: 'Nov 28, 2024',
    },
    {
        id: '1023',
        title: 'Product page 2',
        url: '/product/2',
        status: 'Published',
        type: 'Product',
        Last_updated: 'Nov 29, 2024',
    },
    {
        id: '1024',
        title: 'Landing page 1',
        url: '/landing/1',
        status: 'Unpublished',
        type: 'Landing',
        Last_updated: 'Nov 30, 2024',
    },
    {
        id: '1025',
        title: 'Contact page',
        url: '/contact',
        status: 'Published',
        type: 'Landing',
        Last_updated: 'Dec 01, 2024',
    },
    {
        id: '1026',
        title: 'Regular page 3',
        url: 'Not available',
        status: 'Unpublished',
        type: 'Landing',
        Last_updated: 'Dec 02, 2024',
    },
    {
        id: '1027',
        title: 'Product page 3',
        url: '/product/3',
        status: 'Published',
        type: 'Product',
        Last_updated: 'Dec 03, 2024',
    },
    {
        id: '1028',
        title: 'Home page 3',
        url: '/home-3',
        status: 'Published',
        type: 'Home',
        Last_updated: 'Dec 04, 2024',
    },
    {
        id: '1029',
        title: 'Landing page 2',
        url: '/landing/2',
        status: 'Unpublished',
        type: 'Landing',
        Last_updated: 'Dec 05, 2024',
    },
    {
        id: '1030',
        title: 'Regular page 4',
        url: 'Not available',
        status: 'Unpublished',
        type: 'Landing',
        Last_updated: 'Dec 06, 2024',
    },
    {
        id: '1031',
        title: 'Product page 4',
        url: '/product/4',
        status: 'Published',
        type: 'Product',
        Last_updated: 'Dec 07, 2024',
    },
    {
        id: '1032',
        title: 'Home page 4',
        url: '/home-4',
        status: 'Published',
        type: 'Home',
        Last_updated: 'Dec 08, 2024',
    },
    {
        id: '1033',
        title: 'Landing page 3',
        url: '/landing/3',
        status: 'Unpublished',
        type: 'Landing',
        Last_updated: 'Dec 09, 2024',
    },
    {
        id: '1034',
        title: 'Contact page 2',
        url: '/contact-2',
        status: 'Published',
        type: 'Landing',
        Last_updated: 'Dec 10, 2024',
    },
    {
        id: '1035',
        title: 'About Us page',
        url: '/about',
        status: 'Published',
        type: 'Landing',
        Last_updated: 'Dec 11, 2024',
    },
];

// PageAnalytics data is now managed by AnalyticStore

export const PageStatusOptions = [
    {
        id: 'all',
        content: 'All',
    },
    {
        id: 'published',
        content: 'Published',
    },
    {
        id: 'unpublished',
        content: 'Unpublished',
    },
];
export const sortOptions: any[] = [
    { label: 'Title', value: 'order asc', directionLabel: 'Ascending' },
    { label: 'Title', value: 'order desc', directionLabel: 'Descending' },
    { label: 'Last updated', value: 'customer asc', directionLabel: 'A-Z' },
    { label: 'Last updated', value: 'customer desc', directionLabel: 'Z-A' },
];

export const filterChoices: FilterChoice[] = [
    { label: 'Published', value: 'Published' },
    { label: 'Unpublished', value: 'Unpublished' },
];

export const resourceName = {
    singular: 'Page',
    plural: 'Pages',
};

export const tableHeadings = [
    { title: 'Title' },
    { title: 'Status' },
    { title: 'Add to cart rate', alignment: 'center' },
    { title: 'Product views rate', alignment: 'center' },
    { title: 'Visitors', alignment: 'center' },
    { title: 'Sessions', alignment: 'center' },
    { title: '' },
];

// Cấu hình cũ cho tương thích ngược
export const tableHeadingsOld = [
    { title: 'Title' },
    { title: 'Status' },
    { title: 'Type', alignment: 'center' },
    { title: 'Last updated' },
    { title: '' },
];

export const tabs = [
    {
        id: 1,
        content: 'All',
        panelID: 'all',
    },
    {
        id: 2,
        content: 'Landing',
        panelID: 'landing',
    },
    {
        id: 3,
        content: 'Home',
        panelID: 'home',
    },
    {
        id: 4,
        content: 'Product',
        panelID: 'product',
    },
    {
        id: 5,
        content: 'Collection',
        panelID: 'collection',
    },
    {
        id: 6,
        content: 'List collection',
        panelID: 'list-collection',
    },
];
