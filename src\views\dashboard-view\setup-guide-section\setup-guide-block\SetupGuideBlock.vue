<script setup lang="ts">
import { dashboardLocaleVars } from '@locales/dashboard';
import {
    Card,
    BlockStack,
    InlineStack,
    Text,
    ButtonGroup,
    Popover,
    ActionList,
    Button,
    Box,
    ProgressBar,
    Icon,
} from '@ownego/polaris-vue';
import CheckIcon from '@shopify/polaris-icons/CheckIcon.svg';
import ChevronDownIcon from '@shopify/polaris-icons/ChevronDownIcon.svg';
import ChevronUpIcon from '@shopify/polaris-icons/ChevronUpIcon.svg';
import MenuHorizontalIcon from '@shopify/polaris-icons/MenuHorizontalIcon.svg';
import XIcon from '@shopify/polaris-icons/XIcon.svg';
import { nanoid } from 'nanoid';
import { ref, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';

import { SetupItemSnippet } from './setup-item-snippet';
import { SetupGuideProps } from './types';

const { t } = useI18n();
const props = defineProps(SetupGuideProps);

const expanded = ref(0);
const defaultExpandedIndex = computed(() => props.items?.findIndex((item) => !item.complete) ?? 0);

watch(
    () => props.items,
    () => {
        if (isGuideOpen.value && expanded.value === -1) {
            expanded.value = defaultExpandedIndex.value;
        }
    },
    { deep: true },
);

const isGuideOpen = ref<boolean>(true);
const popoverActive = ref(false);
const accessId = ref(`setup-guide-${nanoid()}`);

const completedItemsLength = computed(() => props.items?.filter((item) => item.complete).length ?? 0);

const toggleGuide = () => {
    isGuideOpen.value = !isGuideOpen.value;

    if (isGuideOpen.value) {
        expanded.value = defaultExpandedIndex.value;
    } else {
        expanded.value = -1;
    }
};

const setExpanded = (itemId: string) => {
    expanded.value = props.items?.findIndex((item) => item.id === itemId) ?? 0;
};

const emit = defineEmits(['stepComplete', 'dismiss']);

const handleStepComplete = (itemId: string) => {
    emit('stepComplete', itemId);
};

const actionListItems = computed(() => [
    {
        content: t(dashboardLocaleVars.setupGuideDismiss),
        onAction: () => props.onDismiss?.(),
        prefix: XIcon,
    },
]);
</script>

<template>
    <Card :padding="'0'">
        <Box :padding="'400'" :paddingBlockEnd="'400'">
            <BlockStack>
                <InlineStack :align="'space-between'" :blockAlign="'center'">
                    <Text :as="'h3'" :variant="'headingMd'">{{ t(dashboardLocaleVars.setupGuideTitle) }}</Text>
                    <ButtonGroup :gap="'tight'" :noWrap="true">
                        <Popover :active="popoverActive" @close="popoverActive = false">
                            <template #activator>
                                <Button
                                    @click="popoverActive = !popoverActive"
                                    variant="tertiary"
                                    :icon="MenuHorizontalIcon"
                                />
                            </template>
                            <ActionList :actionRole="'menuitem'" :items="actionListItems" />
                        </Popover>
                        <Button
                            variant="tertiary"
                            :icon="isGuideOpen ? ChevronUpIcon : ChevronDownIcon"
                            @click="toggleGuide"
                            :ariaControls="accessId"
                        />
                    </ButtonGroup>
                </InlineStack>
                <Text :as="'p'" :variant="'bodyMd'">{{ t(dashboardLocaleVars.setupGuideDescription) }}</Text>
                <div style="margin-top: 0.8rem">
                    <InlineStack :blockAlign="'center'" :gap="200">
                        <div v-if="completedItemsLength === props.items?.length" style="max-height: 1rem">
                            <InlineStack :wrap="false" gap="100">
                                <Icon
                                    :source="CheckIcon"
                                    tone="subdued"
                                    accessibilityLabel="Check icon to indicate completion of Setup Guide"
                                />
                                <Text :as="'p'" :variant="'bodySm'" :tone="'subdued'">{{
                                    t(dashboardLocaleVars.setupGuideDone)
                                }}</Text>
                            </InlineStack>
                        </div>
                        <Text v-else :as="'span'" :variant="'bodySm'">
                            {{ completedItemsLength }} / {{ props.items?.length }}
                            {{ t(dashboardLocaleVars.setupGuideCompleted) }}
                        </Text>
                        <div v-if="completedItemsLength !== props.items?.length" style="width: 100px">
                            <ProgressBar
                                :progress="(completedItemsLength / (props.items?.length || 1)) * 100"
                                :size="'small'"
                                :color="'primary'"
                                animated
                            />
                        </div>
                    </InlineStack>
                </div>
            </BlockStack>
        </Box>

        <div v-if="isGuideOpen">
            <Box :padding="'200'">
                <BlockStack :gap="'100'">
                    <SetupItemSnippet
                        v-for="item in props.items || []"
                        :key="item.id"
                        :complete="item.complete"
                        :expanded="expanded === props.items?.findIndex((i) => i.id === item.id) && isGuideOpen"
                        :title="item.title"
                        :titleKey="item.titleKey"
                        :description="item.description"
                        :descriptionKey="item.descriptionKey"
                        :image="item.image"
                        :primaryButton="item.primaryButton"
                        :secondaryButton="item.secondaryButton"
                        :id="item.id"
                        @set-expanded="setExpanded"
                        @complete="handleStepComplete"
                    />
                </BlockStack>
            </Box>
        </div>

        <Box
            v-if="completedItemsLength === props.items?.length"
            :background="'bg-surface-secondary'"
            :borderBlockStartWidth="'025'"
            :borderColor="'border-secondary'"
            :padding="'300'"
        >
            <InlineStack :align="'end'">
                <s-button @click="props.onDismiss">{{ t(dashboardLocaleVars.setupGuideDismissButton) }}</s-button>
            </InlineStack>
        </Box>
    </Card>
</template>

<style scoped lang="scss"></style>
