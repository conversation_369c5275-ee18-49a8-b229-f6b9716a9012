export interface ImageType {
    url: string;
    alt?: string;
}

export interface ButtonType {
    content?: string;
    contentKey?: string;
    props?: Record<string, any>;
}

export const SetupItemSnippetProps = {
    complete: {
        type: Boolean,
        default: false,
    },
    expanded: {
        type: Boolean,
        default: true,
    },
    title: {
        type: String,
        required: false,
        default: '',
    },
    titleKey: {
        type: String,
        required: false,
        default: '',
    },
    description: {
        type: String,
        required: false,
        default: '',
    },
    descriptionKey: {
        type: String,
        required: false,
        default: '',
    },
    image: {
        type: Object as () => ImageType | null,
        default: null,
    },
    primaryButton: {
        type: Object as () => ButtonType | null,
        default: null,
    },
    secondaryButton: {
        type: Object as () => ButtonType | null,
        default: null,
    },
    id: {
        type: String,
        required: true,
    },
};
