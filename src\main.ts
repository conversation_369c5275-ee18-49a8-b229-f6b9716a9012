import '@assets/styles/index.scss';
// locales
import { commonLocaleEN, commonLocaleVI } from '@locales/common';
import { dashboardLocaleEN, dashboardLocaleVI } from '@locales/dashboard';
import { feedbackLocaleEN, feedbackLocaleVI } from '@locales/feedback';
import { formsLocaleEN, formsLocaleVI } from '@locales/forms';
import { pagesLocaleEN, pagesLocaleVI } from '@locales/pages';
import { settingsLocaleEN, settingsLocaleVI } from '@locales/settings';
import PolarisVue from '@ownego/polaris-vue';
import { createPinia } from 'pinia';
import { createApp } from 'vue';
import { createI18n } from 'vue-i18n';

import App from './App.vue';
import router from './router';

export const i18n = createI18n({
    legacy: false,
    sync: true,
    locale: 'en',
    fallbackLocale: 'en',
    messages: {
        en: {
            ...commonLocaleEN,
            ...dashboardLocaleEN,
            ...feedbackLocaleEN,
            ...formsLocaleEN,
            ...pagesLocaleEN,
            ...settingsLocaleEN,
        },
        vi: {
            ...commonLocaleVI,
            ...dashboardLocaleVI,
            ...feedbackLocaleVI,
            ...formsLocaleVI,
            ...pagesLocaleVI,
            ...settingsLocaleVI,
        },
    },
});

const app = createApp(App);

app.use(createPinia());
app.use(router);
app.use(PolarisVue);
app.use(i18n);
app.config.warnHandler = (msg, instance, trace) => {
    if (msg.includes('Slot "default" invoked outside')) return;
    console.warn(msg, trace);
};
app.mount('#app');
