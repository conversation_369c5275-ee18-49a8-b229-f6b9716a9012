<script lang="ts" setup>
const props = defineProps<{
    id: string;
    title: string;
    primaryAction?: string;
    destructive?: boolean;
}>();

const hideModal = () => {
    const modal = document.getElementById(props.id) as any;
    if (modal) {
        modal.hide();
    }
};
</script>

<template>
    <ui-modal :id="props.id">
        <div>
            <slot />
        </div>

        <ui-title-bar :title="props.title">
            <button v-if="props.primaryAction" variant="primary" :tone="props.destructive ? 'critical' : undefined">
                {{ props.primaryAction }}
            </button>

            <button @click="hideModal">Cancel</button>
        </ui-title-bar>
    </ui-modal>
</template>

<style scoped lang="scss"></style>
