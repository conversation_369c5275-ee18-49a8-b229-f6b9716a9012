<script setup lang="ts">
import Info from '@assets/svgs/info.svg';
import { Box, InlineStack, Pagination } from '@ownego/polaris-vue';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

import { formsLocaleVars } from '@/locales/forms';

const { t } = useI18n();

defineProps<{
    currentPage: number;
    totalPages: number;
    totalItems?: number;
    startItem?: number;
    endItem?: number;
    goToPreviousPage: () => void;
    goToNextPage: () => void;
}>();

const learnMoreText = computed(() => {
    return t(formsLocaleVars.submissionsLearnMore);
});

const submissionsText = computed(() => {
    return t(formsLocaleVars.submissionsTitle);
});
</script>

<template>
    <Box :padding="300">
        <InlineStack :align="'space-between'" style="width: 100%">
            <div>
                <Pagination
                    :hasPrevious="currentPage > 1"
                    :hasNext="currentPage < totalPages"
                    @previous="goToPreviousPage"
                    @next="goToNextPage"
                    :previousTooltip="t(formsLocaleVars.paginationPrevious)"
                    :nextTooltip="t(formsLocaleVars.paginationNext)"
                >
                    {{ currentPage }} {{ t(formsLocaleVars.paginationOf) }} {{ totalPages }}
                </Pagination>
            </div>
            <Box :padding="100">
                <div class="learn-more">
                    <InlineStack :align="'center'" :gap="150">
                        <Info />
                        <Box>
                            {{ learnMoreText }} <a href="#" target="_blank">{{ submissionsText.toLowerCase() }}</a>
                        </Box>
                    </InlineStack>
                </div>
            </Box>
        </InlineStack>
    </Box>
</template>

<style scoped lang="scss">
.learn-more {
    a {
        color: var(--m-blue-base);
        text-decoration: none;

        &:active {
            color: var(--m-blue-base);
        }
    }
}

.items-text {
    margin-top: 0.5rem;
    margin-left: 0.5rem;
}
</style>
