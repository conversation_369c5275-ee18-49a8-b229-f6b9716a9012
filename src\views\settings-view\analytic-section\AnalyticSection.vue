<script setup lang="ts">
import { <PERSON>ge, BlockStack, Button, Card, InlineStack, Text } from '@ownego/polaris-vue';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';

import { settingsLocaleVars } from '@/locales/settings';

const { t } = useI18n();

const isEnabled = ref(false);

const isLoading = ref(false);
declare const shopify: {
    toast: {
        show: (_msg: string) => void;
    };
};
const handleEnable = () => {
    isLoading.value = true;

    setTimeout(() => {
        if (isEnabled.value) {
            isEnabled.value = false;
            shopify?.toast.show('Analytics disabled successfully');
        } else {
            isEnabled.value = true;
            shopify?.toast.show('Analytics enabled successfully');
        }
        isLoading.value = false;
    }, 1000);
};
</script>

<template>
    <s-grid gap="base">
        <s-grid gridTemplateColumns="@container (inline-size <= 700px) 1fr, 2fr 5fr" gap="base">
            <s-box>
                <s-heading>{{ t(settingsLocaleVars.pageAnalyticsTitle) }}</s-heading>
            </s-box>
            <BlockStack gap="400">
                <Card>
                    <InlineStack :align="'start'" :gap="'300'">
                        <Text as="h2" variant="headingMd">EComposer Analytics is </Text>
                        <Badge tone="success" v-if="isEnabled">Enable</Badge>
                        <Badge tone="attention" v-else>Disable</Badge>
                    </InlineStack>
                    <BlockStack gap="400">
                        <Text as="p" variant="bodyMd">
                            Enable EComposer Analytics will give us consent to track your page data & let you view page
                            analytics. This provides you actionable insights to improve your store performance & enhance
                            customer experiences.
                        </Text>
                        <BlockStack>
                            <Text as="strong" variant="bodyMd" fontWeight="bold">
                                By clicking Enable, you agree to EComposer's Terms of Service and Privacy Policy.
                            </Text>
                            <Box> <a class="link"> Learn more</a>. </Box>
                        </BlockStack>
                    </BlockStack>
                </Card>
                <InlineStack :align="'end'">
                    <Button @click="handleEnable" :loading="isLoading">
                        <Text as="span" variant="bodyMd" v-if="isEnabled">Disable</Text>
                        <Text as="span" variant="bodyMd" v-else>Enable</Text>
                    </Button>
                </InlineStack>
            </BlockStack>
        </s-grid>
    </s-grid>
</template>

<style scoped lang="scss">
.link {
    color: rgb(0, 91, 211);
    text-decoration: underline;
    cursor: pointer;
}
</style>
