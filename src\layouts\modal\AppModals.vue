<script setup lang="ts">
import { computed, watch, nextTick, ref } from 'vue';

import { useAppStore } from '@/stores/appStore';

import { UninstallDefaultContent } from './uninstall-default-content';
import { UninstallNextContent } from './uninstall-next-content';

interface CustomHTMLElement extends HTMLElement {
    show?: () => void;
    hide?: () => void;
}

const appStore = useAppStore();
const modal = computed(() => appStore.modal);

const isConfirmUninstall = ref(false);
const closeModal = () => {
    appStore.closeModal();
};

const handleNextStep = () => {
    appStore.openModal('uninstall-next', 'Tell us why');
};

const handleUninstall = () => {
    closeModal();
};
const handleConfirmUninstall = () => {
    isConfirmUninstall.value = true;
};

const singleModalId = 'app-global-modal';

watch(
    () => modal.value,
    async (newVal) => {
        await nextTick();

        if (newVal.isOpen) {
            const modalElement = document.getElementById(singleModalId) as CustomHTMLElement;
            if (modalElement && typeof modalElement.show === 'function') {
                modalElement.show();
            }
        } else {
            const modalElement = document.getElementById(singleModalId) as CustomHTMLElement;
            if (modalElement && typeof modalElement.hide === 'function') {
                modalElement.hide();
            }
        }
    },
    { immediate: true, deep: true },
);
</script>

<template>
    <ui-modal :id="singleModalId" @hidden="closeModal">
        <div>
            <!-- Uninstall Default Content -->
            <div v-if="modal.type === 'uninstall-default'">
                <UninstallDefaultContent />
            </div>

            <!-- Uninstall Next Content -->
            <div v-else-if="modal.type === 'uninstall-next'">
                <UninstallNextContent @confirm="handleConfirmUninstall" :isConfirmUninstall="isConfirmUninstall" />
            </div>
        </div>

        <ui-title-bar :title="modal.title">
            <template v-if="modal.type === 'uninstall-default'">
                <button @click="handleNextStep">Next</button>
            </template>
            <template v-if="modal.type === 'uninstall-next'">
                <button @click="closeModal">Cancel</button>
                <button @click="handleUninstall" disabled variant="primary" tone="critical">Uninstall</button>
            </template>
        </ui-title-bar>
    </ui-modal>
</template>

<style scoped lang="scss"></style>
