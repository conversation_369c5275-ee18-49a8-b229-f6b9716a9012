<script setup lang="ts">
import { BlockStack } from '@ownego/polaris-vue';

import { ShopifyEditions } from '@/components/shopify-editions';

import { BannerDashboardView } from './banner-section';
import { FabSection } from './fab-section';
import { FeatureAppsSection } from './feature-apps-section';
import { SetupGuideView } from './setup-guide-section';
import { SlotUsagesSection } from './slot-usages-section';
import { SupportSection } from './support-section';
import { TemplateSection } from './template-section';
import { WelcomeView } from './welcome-section';
import { WhatNewSection } from './what-new-section';
</script>

<template>
    <s-page>
        <TemplateSection />

        <BlockStack gap="400">
            <ShopifyEditions />
            <BannerDashboardView />
            <SetupGuideView />
            <SlotUsagesSection />
            <WelcomeView />
            <WhatNewSection />
            <FeatureAppsSection />
            <SupportSection />
        </BlockStack>
        <FabSection />
    </s-page>
</template>

<style scoped lang="scss">
:deep(.s-page) {
    position: relative;
}
</style>
