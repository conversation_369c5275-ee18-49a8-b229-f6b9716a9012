<script setup lang="ts">
import AlertDiamondIcon from '@assets/svgs/alert-diamond.svg';
import { ModalBase } from '@components/modal-base';
import {
    BlockStack,
    Box,
    Card,
    DropZone,
    DropZoneFileUpload,
    IndexTable,
    IndexTableCell,
    IndexTableRow,
    InlineStack,
    Spinner,
    Text,
} from '@ownego/polaris-vue';
import { ref, nextTick, onMounted, onUnmounted } from 'vue';
import { useI18n } from 'vue-i18n';

import { pagesLocaleVars } from '@/locales/pages';

const { t } = useI18n();
const files = ref<File[]>([]);
const dropZoneRef = ref();
const modalContainerRef = ref();
const modalBaseRef = ref();
let resizeObserver: ResizeObserver | null = null;
let isObserverSetup = false;
const isTooBig = ref(false);
const maxHeight = 300;
const isContentLoaded = ref(false);
const isContentVisible = ref(false);

const handleDrop = (_dropFiles: File[], acceptedFiles: File[]) => {
    files.value = [...files.value, ...acceptedFiles];
};

const checkContainerHeight = async () => {
    await nextTick();
    if (modalContainerRef.value) {
        modalContainerRef.value.style.visibility = 'hidden';
        modalContainerRef.value.style.display = 'block';
        modalContainerRef.value.style.position = 'absolute';

        const height = modalContainerRef.value.offsetHeight;

        modalContainerRef.value.style.visibility = '';
        modalContainerRef.value.style.display = '';
        modalContainerRef.value.style.position = '';

        isTooBig.value = height > maxHeight;
        isContentLoaded.value = true;
        isContentVisible.value = !isTooBig.value;

        return !isTooBig.value;
    }
    return true;
};

const setupResizeObserver = async () => {
    await nextTick();

    const modalContainer = modalContainerRef.value;
    if (modalContainer && !resizeObserver) {
        isObserverSetup = true;
        resizeObserver = new ResizeObserver(() => {
            window.dispatchEvent(new Event('resize'));
            if (dropZoneRef.value?.updateSize) {
                dropZoneRef.value.updateSize();
            }

            checkContainerHeight();
        });
        resizeObserver.observe(modalContainer);
    }
};

const prepareModal = async () => {
    if (!isObserverSetup) {
        await setupResizeObserver();
    }

    const heightOK = await checkContainerHeight();
    return heightOK;
};

const showModal = async () => {
    isContentLoaded.value = false;
    isContentVisible.value = false;

    const modal = document.getElementById('import-page-modal') as any;
    if (modal) {
        modal.show();

        setTimeout(async () => {
            const isReady = await prepareModal();
            if (!isReady) {
            }
        }, 300);
    }
};

onMounted(() => {
    checkContainerHeight();
});

onUnmounted(() => {
    if (resizeObserver) {
        resizeObserver.disconnect();
        resizeObserver = null;
    }
});

const orders = [
    {
        id: '1020',
        title: 'Name template',
        display_on: 'Product page',
    },
];

defineExpose({
    showModal,
    prepareModal,
    isTooBig,
    isContentLoaded,
    isContentVisible,
});
</script>

<template>
    <ModalBase
        ref="modalBaseRef"
        :id="'import-page-modal'"
        :title="t(pagesLocaleVars.modalImportTitle)"
        :primary-action="t(pagesLocaleVars.modalImportConfirm)"
        :destructive="false"
    >
        <div ref="modalContainerRef" class="import__container">
            <div v-if="!isContentLoaded" class="loading-container">
                <Spinner accessibilityLabel="Spinner example" size="small" />;
            </div>

            <Box v-if="isContentLoaded" :padding="'400'" :class="{ 'content-hidden': !isContentVisible }">
                <div v-if="isTooBig" class="container-too-big">
                    <BlockStack gap="400">
                        <Box :padding="'200'" class="import__container--alert">
                            <InlineStack :align="'start'" gap="200">
                                <AlertDiamondIcon width="20px" height="20px" />
                                <Text as="p" variant="bodyMd">
                                    {{ t(pagesLocaleVars.modalImportMessage) }}
                                </Text>
                            </InlineStack>
                        </Box>
                    </BlockStack>
                </div>
                <div v-else-if="files.length" :style="{ padding: '0' }">
                    <BlockStack gap="400">
                        <Card :padding="'none'">
                            <IndexTable
                                :headings="[{ title: t(pagesLocaleVars.pageTableTitle) }, { title: 'Display on' }]"
                                :item-count="2"
                                :selectable="false"
                            >
                                <IndexTableRow
                                    v-for="({ id, title, display_on }, index) in orders"
                                    :id="id"
                                    :key="id"
                                    :position="index"
                                >
                                    <IndexTableCell class="index-table-cell-title cell_warper">
                                        <Text variant="bodyMd" as="span">{{ title }}</Text>
                                    </IndexTableCell>
                                    <IndexTableCell class="cell_warper">
                                        <Text as="span"> {{ display_on }}</Text>
                                    </IndexTableCell>
                                </IndexTableRow>
                            </IndexTable>
                        </Card>
                        <Text as="p" variant="bodyMd">{{ t(pagesLocaleVars.modalImportMessage) }}</Text>
                    </BlockStack>
                </div>
                <div v-else>
                    <BlockStack :gap="'400'">
                        <Box :padding="'200'" class="import__container--alert">
                            <InlineStack :align="'start'" gap="200">
                                <AlertDiamondIcon width="20px" height="20px" />
                                <Text as="p" variant="bodyMd"> {{ t(pagesLocaleVars.modalImportMessage) }}</Text>
                            </InlineStack>
                        </Box>
                        <DropZone ref="dropZoneRef" @drop="handleDrop">
                            <DropZoneFileUpload :action-hint="t(pagesLocaleVars.modalImportAcceptFileHint)" />
                        </DropZone>
                        <Box>
                            <Text as="p" variant="bodyMd">
                                {{ t(pagesLocaleVars.modalImportLearnMore) }} <a class="link">Export & Import pages</a>.
                                or contact us for
                                <a class="link">{{ t(pagesLocaleVars.modalImportSupport) }}</a>
                            </Text>
                        </Box>
                    </BlockStack>
                </div>
            </Box>
        </div>
    </ModalBase>
</template>
<style scoped lang="scss">
.import__container {
    position: relative;
    min-height: 150px;

    a {
        color: var(--m-blue-base);
    }

    .index-table-cell-title {
        width: 63%;
    }

    .import__container--alert {
        background-color: var(--m-error-banner);
        border-radius: 8px;
        color: var(--m-error-banner-text);
    }

    .link {
        color: var(--m-blue-base);
    }
}

.container-too-big {
    max-height: 200px;
    overflow: auto;
}

.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    min-height: 100px;
}

.content-hidden {
    display: none;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}
</style>
