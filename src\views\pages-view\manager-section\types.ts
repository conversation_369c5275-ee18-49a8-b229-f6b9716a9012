export interface Pages {
    [key: string]: unknown;
    id: string;
    title: string;
    url: string;
    status: string;
    type: string;
    Last_updated: string;
    analyticId?: string;
}

export interface FilterChoice {
    label: string;
    value: string;
}

export interface Tab {
    content: string;
    index: number;
    onAction: () => void;
    id: string;
    isLocked: boolean;
    actions: any[];
}

export interface AppliedFilter {
    name: string;
    label: string;
    onRemove: () => void;
}
