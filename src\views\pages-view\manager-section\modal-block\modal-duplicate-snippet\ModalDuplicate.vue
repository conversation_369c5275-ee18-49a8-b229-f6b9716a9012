<script setup lang="ts">
import { ModalBase } from '@components/modal-base';
import { Box } from '@ownego/polaris-vue';
import { useI18n } from 'vue-i18n';

import { pagesLocaleVars } from '@/locales/pages';

const { t } = useI18n();
</script>
<template>
    <ModalBase
        :id="'duplicate-page-modal'"
        :title="t(pagesLocaleVars.modalDuplicateTitle)"
        :primary-action="t(pagesLocaleVars.modalDuplicateConfirm)"
        :destructive="false"
    >
        <Box :padding="'400'">
            <p>{{ t(pagesLocaleVars.modalDuplicateMessage) }}</p>
        </Box>
    </ModalBase>
</template>

<style scoped lang="scss"></style>
