const makeSettingsLocaleVars = (text: string) => `SETTINGS_${text}`;

const settingsLocaleVars = {
    generalTab: makeSettingsLocaleVars('GENERAL_TAB'),
    plansTab: makeSettingsLocaleVars('PLANS_TAB'),
    usagesTab: makeSettingsLocaleVars('USAGES_TAB'),
    analyticsTab: makeSettingsLocaleVars('ANALYTICS_TAB'),

    generalTitle: makeSettingsLocaleVars('GENERAL_TITLE'),
    generalDescription: makeSettingsLocaleVars('GENERAL_DESCRIPTION'),
    storeInformation: makeSettingsLocaleVars('STORE_INFORMATION'),
    storeOwner: makeSettingsLocaleVars('STORE_OWNER'),
    shopifyDomain: makeSettingsLocaleVars('SHOPIFY_DOMAIN'),
    storefrontPassword: makeSettingsLocaleVars('STOREFRONT_PASSWORD'),
    editStoreName: makeSettingsLocaleVars('EDIT_STORE_NAME'),
    storefrontPasswordDescription: makeSettingsLocaleVars('STOREFRONT_PASSWORD_DESCRIPTION'),
    turnOffPassword: makeSettingsLocaleVars('TURN_OFF_PASSWORD'),

    usageTitle: makeSettingsLocaleVars('USAGE_TITLE'),
    usageDescription: makeSettingsLocaleVars('USAGE_DESCRIPTION'),

    uninstallationTitle: makeSettingsLocaleVars('UNINSTALLATION_TITLE'),

    pageAnalyticsTitle: makeSettingsLocaleVars('PAGE_ANALYTICS_TITLE'),

    plansTitle: makeSettingsLocaleVars('PLANS_TITLE'),
    monthly: makeSettingsLocaleVars('MONTHLY'),
    annual: makeSettingsLocaleVars('ANNUAL'),
    savePercent: makeSettingsLocaleVars('SAVE_PERCENT'),
    seeFullPricing: makeSettingsLocaleVars('SEE_FULL_PRICING'),
    hideFullPricing: makeSettingsLocaleVars('HIDE_FULL_PRICING'),
    fullPricingComparison: makeSettingsLocaleVars('FULL_PRICING_COMPARISON'),

    freePlan: makeSettingsLocaleVars('FREE_PLAN'),
    payAsYouGoPlan: makeSettingsLocaleVars('PAY_AS_YOU_GO_PLAN'),
    enterprisePlan: makeSettingsLocaleVars('ENTERPRISE_PLAN'),
    planDescription: makeSettingsLocaleVars('PLAN_DESCRIPTION'),
    mostPopular: makeSettingsLocaleVars('MOST_POPULAR'),
    current: makeSettingsLocaleVars('CURRENT'),
    month: makeSettingsLocaleVars('MONTH'),
    year: makeSettingsLocaleVars('YEAR'),
    off: makeSettingsLocaleVars('OFF'),
    selectPlan: makeSettingsLocaleVars('SELECT_PLAN'),
    downgrade: makeSettingsLocaleVars('DOWNGRADE'),
    publishedSlots: makeSettingsLocaleVars('PUBLISHED_SLOTS'),
    unlimitedPublishedSlots: makeSettingsLocaleVars('UNLIMITED_PUBLISHED_SLOTS'),
    processOrders: makeSettingsLocaleVars('PROCESS_ORDERS'),
    amazingFeature: makeSettingsLocaleVars('AMAZING_FEATURE'),
    anotherFeature: makeSettingsLocaleVars('ANOTHER_FEATURE'),
    customerSupport: makeSettingsLocaleVars('CUSTOMER_SUPPORT'),

    refundPolicy: makeSettingsLocaleVars('REFUND_POLICY'),
    refundPolicyTitle: makeSettingsLocaleVars('REFUND_POLICY_TITLE'),
    refundPolicyItem1: makeSettingsLocaleVars('REFUND_POLICY_ITEM1'),
    refundPolicyItem2: makeSettingsLocaleVars('REFUND_POLICY_ITEM2'),
    refundPolicyItem3: makeSettingsLocaleVars('REFUND_POLICY_ITEM3'),
    refundPolicyItem4: makeSettingsLocaleVars('REFUND_POLICY_ITEM4'),

    faq: makeSettingsLocaleVars('FAQ'),
    faqQuestion1: makeSettingsLocaleVars('FAQ_QUESTION1'),
    faqAnswer1: makeSettingsLocaleVars('FAQ_ANSWER1'),
    faqQuestion2: makeSettingsLocaleVars('FAQ_QUESTION2'),
    faqAnswer2: makeSettingsLocaleVars('FAQ_ANSWER2'),
    faqQuestion3: makeSettingsLocaleVars('FAQ_QUESTION3'),
    faqAnswer3: makeSettingsLocaleVars('FAQ_ANSWER3'),
    faqQuestion4: makeSettingsLocaleVars('FAQ_QUESTION4'),
    faqAnswer4: makeSettingsLocaleVars('FAQ_ANSWER4'),

    slotCategory: makeSettingsLocaleVars('SLOT_CATEGORY'),
    businessGrowthCategory: makeSettingsLocaleVars('BUSINESS_GROWTH_CATEGORY'),
    templatesCategory: makeSettingsLocaleVars('TEMPLATES_CATEGORY'),
    pageTypesCategory: makeSettingsLocaleVars('PAGE_TYPES_CATEGORY'),
    supportCategory: makeSettingsLocaleVars('SUPPORT_CATEGORY'),

    publishedFeature: makeSettingsLocaleVars('PUBLISHED_FEATURE'),
    draftFeature: makeSettingsLocaleVars('DRAFT_FEATURE'),
    abTestingFeature: makeSettingsLocaleVars('AB_TESTING_FEATURE'),
    croTemplatesFeature: makeSettingsLocaleVars('CRO_TEMPLATES_FEATURE'),
    regularPageFeature: makeSettingsLocaleVars('REGULAR_PAGE_FEATURE'),
    homepageFeature: makeSettingsLocaleVars('HOMEPAGE_FEATURE'),
    productPageFeature: makeSettingsLocaleVars('PRODUCT_PAGE_FEATURE'),
    collectionPageFeature: makeSettingsLocaleVars('COLLECTION_PAGE_FEATURE'),
    listCollectionsPageFeature: makeSettingsLocaleVars('LIST_COLLECTIONS_PAGE_FEATURE'),
    emailSupportFeature: makeSettingsLocaleVars('EMAIL_SUPPORT_FEATURE'),
    liveChatFeature: makeSettingsLocaleVars('LIVE_CHAT_FEATURE'),
    teamViewFeature: makeSettingsLocaleVars('TEAM_VIEW_FEATURE'),

    unlimited: makeSettingsLocaleVars('UNLIMITED'),
    priority: makeSettingsLocaleVars('PRIORITY'),
    slot: makeSettingsLocaleVars('SLOT'),
    slots: makeSettingsLocaleVars('SLOTS'),
};

export default settingsLocaleVars;
