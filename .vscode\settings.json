{"explorer.fileNesting.enabled": true, "explorer.fileNesting.patterns": {"tsconfig.json": "tsconfig.*.json, env.d.ts", "vite.config.*": "jsconfig*, vitest.config.*, cypress.config.*, playwright.config.*", "package.json": "package-lock.json, pnpm*, .yarnrc*, yarn*, .eslint*, eslint*, .oxlint*, oxlint*, .prettier*, prettier*, .editorconfig"}, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.quickSuggestions": {"strings": true}, "cSpell.words": ["Linethought", "Textbox"], "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[liquid]": {"editor.defaultFormatter": "Shopify.theme-check-vscode"}, "[vue]": {"editor.defaultFormatter": "Vue.volar"}}