<script setup lang="ts">
import { dashboardLocaleVars } from '@locales/dashboard';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';

import { setupGuideItems } from './configs';
import { SetupGuide } from './setup-guide-block';

const { t } = useI18n();
const showGuide = ref(true);
const items = ref([...setupGuideItems]);

const onStepComplete = async (id: string) => {
    try {
        items.value = items.value.map((item) => (item.id === id ? { ...item, complete: !item.complete } : item));
    } catch (e) {
        console.error(e);
        items.value = items.value.map((item) => (item.id === id ? { ...item, complete: !item.complete } : item));
    }
};

const handleDismiss = () => {
    showGuide.value = false;
    items.value = [...setupGuideItems];
};
</script>

<template>
    <div>
        <s-button v-if="!showGuide" @click="showGuide = true">{{
            t(dashboardLocaleVars.setupGuideShowButton)
        }}</s-button>
        <SetupGuide v-else :items="items" @dismiss="handleDismiss" @step-complete="onStepComplete" />
    </div>
</template>

<style scoped lang="scss"></style>
