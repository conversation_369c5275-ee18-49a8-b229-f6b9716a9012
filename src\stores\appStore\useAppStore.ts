import { defineStore } from 'pinia';

import type { AppStoreState, ModalState } from './types';

export const useAppStore = defineStore('app', {
    state: (): AppStoreState => ({
        isAppLoading: true,
        isAuthenticated: false,
        token: null,
        modal: {
            type: 'default',
            isOpen: false,
            title: '',
            errorMessage: '',
            successMessage: '',
            isLoading: false,
            isSuccess: false,
            isError: false,
        },
    }),
    actions: {
        setIsAppLoading(isAppLoading: boolean) {
            this.isAppLoading = isAppLoading;
        },
        setIsAuthenticated(isAuthenticated: boolean) {
            this.isAuthenticated = isAuthenticated;
        },
        setToken(token: string) {
            this.token = token;
        },
        setModalState(modalState: Partial<ModalState>) {
            this.modal = { ...this.modal, ...modalState };
        },
        openModal(type: ModalState['type'], title = '') {
            this.modal = {
                ...this.modal,
                type,
                isOpen: true,
                title,
                errorMessage: '',
                successMessage: '',
                isLoading: false,
                isSuccess: false,
                isError: false,
            };
        },
        openConfirmUninstallModal() {
            this.modal = {
                ...this.modal,
                isSuccess: true,
            };
        },
        closeModal() {
            this.modal = {
                ...this.modal,
                isOpen: false,
            };
        },
    },
});
