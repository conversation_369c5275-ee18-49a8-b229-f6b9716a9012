import { defineStore } from 'pinia';
import { computed, ref, watch } from 'vue';

import type { ChartItem, DateRange, DataPoint } from '@/views/analytic-view/analytic-detail-section/chart-block/types';

import { usePagesStore } from '@/stores/pagesStore';
import {
    analyticDashboardMock,
    analyticChartMock,
} from '@/views/analytic-view/analytic-detail-section/chart-block/config';

import type { PageAnalytic, PageWithAnalytics } from './types';

// Utility function để tạo data cố định dựa trên seed
function seededRandom(seed: string): number {
    let hash = 0;
    for (let i = 0; i < seed.length; i++) {
        const char = seed.charCodeAt(i);
        hash = (hash << 5) - hash + char;
        hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash) / 2147483647; // Normalize to 0-1
}

// Function để generate consistent data dựa trên pageId và period
function generateConsistentData(pageId: string, period: DateRange, dataType: string): any {
    const seed = `${pageId}-${period.alias}-${dataType}`;
    const random = seededRandom(seed);

    switch (dataType) {
        case 'visitors':
            return Math.floor(random * 5000) + 500; // 500-5500
        case 'sessions':
            return Math.floor(random * 8000) + 800; // 800-8800
        case 'addToCartRate':
            return Math.round((random * 10 + 1) * 10) / 10; // 1.0-11.0%
        case 'productViewsRate':
            return Math.round((random * 30 + 5) * 10) / 10; // 5.0-35.0%
        default:
            return Math.floor(random * 1000);
    }
}

export const useAnalyticStore = defineStore('analytic', () => {
    // Get pagesStore instance
    const pagesStore = usePagesStore();

    // Detail view state
    const items = ref<ChartItem[]>([]);
    const isLoading = ref<boolean>(false);
    const error = ref<string | null>(null);
    const activeDataKey = ref<string | null>(null);
    const analyticList = ref<PageAnalytic[]>([]);
    const currentAnalytic = ref<PageAnalytic | null>(null);

    watch(
        () => currentAnalytic.value?.items,
        (newItems) => {
            if (newItems && newItems.length > 0) {
                items.value = [...newItems]; 
            }
        },
        { immediate: true, deep: true },
    );

    // Manager view state
    const queryValue = ref<string>('');
    const statusFilter = ref<string[]>([]);
    const typeFilter = ref<string>('All');
    const currentPage = ref<number>(1);
    const itemsPerPage = ref<number>(6);

    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    const sevenDaysAgo = new Date(today);
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const currentPeriod = ref<DateRange>({
        alias: 'last7days',
        title: 'Last 7 days',
        period: {
            since: sevenDaysAgo,
            until: yesterday,
        },
    });

    const comparisonMode = ref<'previous_period' | 'previous_year' | 'custom'>('previous_period');

    const graphData = ref<{
        sales: { date: string; value: number }[];
        comparison?: { date: string; value: number }[];
    }>({
        sales: analyticChartMock,
    });

    // Getters
    const getItemByKey = computed(() => {
        return (key: string) => items.value.find((item) => item.key === key);
    });

    const getAnalyticByPageId = computed(() => {
        return (pageId: string) => analyticList.value.find((analytic) => analytic.pageId === pageId);
    });

    const formattedSalesData = computed((): DataPoint[] => {
        if (activeDataKey.value) {
            const selectedItem = items.value.find((item) => item.key === activeDataKey.value);

            if (selectedItem) {
                const baseValue = selectedItem.value;
                const percentChange = selectedItem.percent / 100;

                return graphData.value.sales.map((d, index, arr) => {
                    const position = index / (arr.length - 1);
                    const trend = Math.pow(position, 1.5);

                    const value = baseValue * (0.7 + trend * 0.6 * (1 + percentChange));

                    return {
                        date: new Date(d.date),
                        value: Math.max(5, Math.round(value / 10)),
                    };
                });
            }
        }

        return graphData.value.sales.map((d) => ({
            date: new Date(d.date),
            value: d.value,
        }));
    });

    const formattedComparisonData = computed((): DataPoint[] | null => {
        if (!graphData.value.comparison) return null;

        if (activeDataKey.value) {
            const selectedItem = items.value.find((item) => item.key === activeDataKey.value);

            if (selectedItem) {
                const baseValue = selectedItem.lastPeriod;

                return graphData.value.comparison.map((d, index, arr) => {
                    const position = index / (arr.length - 1);
                    const trend = Math.pow(position, 1.2);

                    const value = baseValue * (0.8 + trend * 0.4);

                    return {
                        date: new Date(d.date),
                        value: Math.max(3, Math.round(value / 10)),
                    };
                });
            }
        }

        return graphData.value.comparison.map((d) => ({
            date: new Date(d.date),
            value: d.value,
        }));
    });

    const totalRevenue = computed((): number => {
        const revenueItem = items.value.find((item) => item.key === 'offersRevenue');
        return revenueItem?.value || 0;
    });

    async function fetchAnalyticData(period: DateRange): Promise<void> {
        isLoading.value = true;
        error.value = null;

        try {
            const currentActiveKey = activeDataKey.value;

            await new Promise((resolve) => setTimeout(resolve, 200));

            if (!currentAnalytic.value) {
                console.warn('No current analytic set, cannot fetch data');
                isLoading.value = false;
                return;
            }
            if (currentAnalytic.value.items && currentAnalytic.value.items.length > 0) {
                items.value = currentAnalytic.value.items;
            } else {
                const pageId = currentAnalytic.value.pageId;
                items.value = analyticDashboardMock.map((item) => {
                    const value = generateConsistentData(pageId, period, item.key);
                    const lastPeriodValue = generateConsistentData(pageId + '-prev', period, item.key);
                    const percent = Math.round(((value - lastPeriodValue) / lastPeriodValue) * 100);

                    return {
                        ...item,
                        value,
                        percent,
                        lastPeriod: lastPeriodValue,
                    };
                });
            }

            currentPeriod.value = period;

            if (currentAnalytic.value.graphData) {
                graphData.value = currentAnalytic.value.graphData;
            } else {
                const pageId = currentAnalytic.value.pageId;
                const daysInRange = Math.max(
                    Math.round(
                        (period.period.until.getTime() - period.period.since.getTime()) / (1000 * 60 * 60 * 24),
                    ) + 1,
                    7,
                );

                const newSalesData = Array.from({ length: daysInRange }, (_, i) => {
                    const date = new Date(period.period.since);
                    date.setDate(date.getDate() + i);

                    const seed = `${pageId}-${period.alias}-chart-${i}`;
                    const random = seededRandom(seed);

                    const trendFactor = (i / (daysInRange - 1)) * 0.5 + 0.5;
                    const value = Math.floor(random * 15 * trendFactor) + 5;

                    return {
                        date: date.toISOString().split('T')[0],
                        value,
                    };
                });

                graphData.value = {
                    ...graphData.value,
                    sales: newSalesData,
                };
            }

            if (currentActiveKey) {
                const keyExists = items.value.some((item) => item.key === currentActiveKey);
                if (keyExists) {
                    activeDataKey.value = currentActiveKey;
                } else if (items.value.length > 0) {
                    activeDataKey.value = items.value[0].key;
                }
            } else if (items.value.length > 0) {
                activeDataKey.value = items.value[0].key;
            }

            updateCurrentAnalyticData();
        } catch (err) {
            error.value = err instanceof Error ? err.message : 'Failed to fetch analytic data';
        } finally {
            isLoading.value = false;
        }
    }

    function updateDateRange(range: DateRange): void {
        currentPeriod.value = range;

        updateAllAnalyticsForPeriod(range);

        if (currentAnalytic.value && currentAnalytic.value.pageId) {
            fetchAnalyticData(range);
        }
    }

    function setComparisonMode(mode: 'previous_period' | 'previous_year' | 'custom'): void {
        comparisonMode.value = mode;
        fetchComparisonData();
    }

    async function fetchComparisonData(): Promise<void> {
        if (!currentPeriod.value) return;

        try {
            await new Promise((resolve) => setTimeout(resolve, 100));

            const period = currentPeriod.value;
            const daysInRange = Math.max(
                Math.round((period.period.until.getTime() - period.period.since.getTime()) / (1000 * 60 * 60 * 24)) + 1,
                7,
            );

            let startDate: Date;

            if (comparisonMode.value === 'previous_period') {
                const periodLength = period.period.until.getTime() - period.period.since.getTime();
                startDate = new Date(period.period.since.getTime() - periodLength);
            } else if (comparisonMode.value === 'previous_year') {
                startDate = new Date(period.period.since);
                startDate.setFullYear(startDate.getFullYear() - 1);
            } else {
                startDate = new Date(period.period.since);
            }

            const pageId = currentAnalytic.value?.pageId || 'default';
            const comparisonData = Array.from({ length: daysInRange }, (_, i) => {
                const date = new Date(startDate);
                date.setDate(date.getDate() + i);

                const seed = `${pageId}-${period.alias}-comparison-${comparisonMode.value}-${i}`;
                const random = seededRandom(seed);
                const trendFactor = (i / (daysInRange - 1)) * 0.4 + 0.6;
                const value = Math.floor(random * 15 * trendFactor) + 3;

                return {
                    date: date.toISOString().split('T')[0],
                    value,
                };
            });

            graphData.value = {
                ...graphData.value,
                comparison: comparisonData,
            };
        } catch (err) {
            console.error('Failed to fetch comparison data:', err);
        }
    }

    function setActiveDataKey(key: string): void {
        activeDataKey.value = key;
    }

    const activeItemData = computed(() => {
        if (!activeDataKey.value) return null;
        return items.value.find((item) => item.key === activeDataKey.value) || null;
    });

    async function fetchPageAnalytics(): Promise<void> {
        isLoading.value = true;
        error.value = null;

        try {
            if (pagesStore.pages.length === 0) {
                pagesStore.fetchPages();
                await new Promise((resolve) => setTimeout(resolve, 1100));
            }

            if (analyticList.value.length === 0 || analyticList.value.length !== pagesStore.pages.length) {
                const mockAnalytics: PageAnalytic[] = pagesStore.pages.map((page) => {
                    const items = analyticDashboardMock.map((item) => {
                        const value = generateConsistentData(page.id, currentPeriod.value, item.key);
                        const lastPeriodValue = generateConsistentData(
                            page.id + '-prev',
                            currentPeriod.value,
                            item.key,
                        );
                        const percent = Math.round(((value - lastPeriodValue) / lastPeriodValue) * 100);

                        return {
                            ...item,
                            value,
                            percent,
                            lastPeriod: lastPeriodValue,
                        };
                    });

                    const visitorsItem = items.find((item) => item.key === 'visitors');
                    const sessionsItem = items.find((item) => item.key === 'sessions');
                    const addToCartRateItem = items.find((item) => item.key === 'addToCartRate');
                    const productViewsRateItem = items.find((item) => item.key === 'productViewsRate');

                    return {
                        id: page.id,
                        pageId: page.id,

                        addToCartRate:
                            addToCartRateItem?.value ||
                            generateConsistentData(page.id, currentPeriod.value, 'addToCartRate'),
                        productViewsRate:
                            productViewsRateItem?.value ||
                            generateConsistentData(page.id, currentPeriod.value, 'productViewsRate'),
                        visitors:
                            visitorsItem?.value || generateConsistentData(page.id, currentPeriod.value, 'visitors'),
                        sessions:
                            sessionsItem?.value || generateConsistentData(page.id, currentPeriod.value, 'sessions'),

                        items,

                        graphData: {
                            sales: Array.from({ length: 7 }, (_, j) => {
                                const date = new Date();
                                date.setDate(date.getDate() - 7 + j);
                                const seed = `${page.id}-${currentPeriod.value.alias}-chart-${j}`;
                                const random = seededRandom(seed);
                                const trendFactor = (j / 6) * 0.5 + 0.5;
                                const value = Math.floor(random * 15 * trendFactor) + 5;

                                return {
                                    date: date.toISOString().split('T')[0],
                                    value,
                                };
                            }),
                        },

                        created_at: new Date(
                            Date.now() - seededRandom(page.id + '-created') * 10000000000,
                        ).toISOString(),
                        updated_at: new Date().toISOString(),
                    };
                });

                analyticList.value = mockAnalytics;

                if (!currentAnalytic.value && mockAnalytics.length > 0) {
                    currentAnalytic.value = mockAnalytics[0];
                }
            }
        } catch (err) {
            error.value = err instanceof Error ? err.message : 'Failed to fetch page analytics';
        } finally {
            isLoading.value = false;
        }
    }

    function setCurrentAnalytic(analyticId: string): void {
        const analytic = analyticList.value.find((a) => a.id === analyticId);
        if (analytic) {
            currentAnalytic.value = analytic;

            items.value = analytic.items;
            graphData.value = analytic.graphData;

            if (items.value.length > 0) {
                activeDataKey.value = items.value[0].key;
            }
        }
    }

    function updateCurrentAnalyticData(): void {
        if (currentAnalytic.value) {
            const analyticIndex = analyticList.value.findIndex((a) => a.id === currentAnalytic.value!.id);
            if (analyticIndex !== -1) {
                analyticList.value[analyticIndex] = {
                    ...analyticList.value[analyticIndex],
                    items: items.value,
                    graphData: graphData.value,

                    visitors:
                        items.value.find((item) => item.key === 'visitors')?.value ||
                        analyticList.value[analyticIndex].visitors,
                    sessions:
                        items.value.find((item) => item.key === 'sessions')?.value ||
                        analyticList.value[analyticIndex].sessions,
                    addToCartRate:
                        items.value.find((item) => item.key === 'addToCartRate')?.value ||
                        analyticList.value[analyticIndex].addToCartRate,
                    productViewsRate:
                        items.value.find((item) => item.key === 'productViewsRate')?.value ||
                        analyticList.value[analyticIndex].productViewsRate,
                    updated_at: new Date().toISOString(),
                };

                currentAnalytic.value = analyticList.value[analyticIndex];
            }
        }
    }

    const pagesWithAnalytics = computed((): PageWithAnalytics[] => {
        return pagesStore.pages.map((page) => {
            const analytics = analyticList.value.find((a) => a.pageId === page.id);

            if (!analytics) {
                const defaultAnalytics: PageAnalytic = {
                    id: page.id,
                    pageId: page.id,
                    addToCartRate: 0,
                    productViewsRate: 0,
                    visitors: 0,
                    sessions: 0,
                    items: [],
                    graphData: { sales: [] },
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString(),
                };
                return {
                    ...page,
                    analytics: defaultAnalytics,
                };
            }

            let syncedAnalytics = analytics;
            if (currentAnalytic.value && currentAnalytic.value.id === analytics.id) {
                syncedAnalytics = {
                    ...analytics,
                    items: items.value,
                    graphData: graphData.value,

                    visitors: items.value.find((item) => item.key === 'visitors')?.value || analytics.visitors,
                    sessions: items.value.find((item) => item.key === 'sessions')?.value || analytics.sessions,
                    addToCartRate:
                        items.value.find((item) => item.key === 'addToCartRate')?.value || analytics.addToCartRate,
                    productViewsRate:
                        items.value.find((item) => item.key === 'productViewsRate')?.value ||
                        analytics.productViewsRate,
                };
            }

            return {
                ...page,
                analytics: syncedAnalytics,
            };
        });
    });

    const filteredAnalytics = computed(() => {
        let filtered = [...pagesWithAnalytics.value];

        if (typeFilter.value && typeFilter.value !== 'All') {
            filtered = filtered.filter((pageWithAnalytics) => pageWithAnalytics.type === typeFilter.value);
        }

        if (statusFilter.value.length > 0) {
            if (statusFilter.value.includes('All') && statusFilter.value.length === 1) {
            } else {
                filtered = filtered.filter((pageWithAnalytics) =>
                    statusFilter.value.includes(pageWithAnalytics.status),
                );
            }
        }

        if (queryValue.value) {
            filtered = filtered.filter((pageWithAnalytics) =>
                pageWithAnalytics.title.toLowerCase().includes(queryValue.value.toLowerCase()),
            );
        }

        return filtered;
    });

    const paginatedAnalytics = computed(() => {
        const start = (currentPage.value - 1) * itemsPerPage.value;
        const end = start + itemsPerPage.value;
        return filteredAnalytics.value.slice(start, end);
    });

    const totalPages = computed(() => Math.max(1, Math.ceil(filteredAnalytics.value.length / itemsPerPage.value)));

    const totalAnalytics = computed(() => filteredAnalytics.value.length);

    function setQueryValue(value: string): void {
        queryValue.value = value;
        currentPage.value = 1;
    }

    function setStatusFilter(statuses: string[]): void {
        statusFilter.value = statuses;
        currentPage.value = 1;
    }

    function setTypeFilter(type: string): void {
        typeFilter.value = type;
        currentPage.value = 1;
    }

    function setCurrentPage(page: number): void {
        currentPage.value = page;
    }

    function goToPreviousPage(): void {
        if (currentPage.value > 1) {
            currentPage.value--;
        }
    }

    function goToNextPage(): void {
        if (currentPage.value < totalPages.value) {
            currentPage.value++;
        }
    }

    function updateAnalyticStatus(id: string, newStatus: string): void {
        pagesStore.updatePageStatus(id, newStatus);
    }

    function getAnalyticById(id: string): PageAnalytic | undefined {
        return analyticList.value.find((analytic) => analytic.id === id);
    }

    function loadAnalyticDataInstantly(analyticId: string): boolean {
        const analytic = getAnalyticById(analyticId);
        if (analytic && analytic.items && analytic.items.length > 0) {
            items.value = [...analytic.items];
            graphData.value = { ...analytic.graphData };
            currentAnalytic.value = analytic;

            if (analytic.items.length > 0) {
                activeDataKey.value = analytic.items[0].key;
            }

            return true;
        }
        return false;
    }

    function preloadAnalyticData(pageId: string): void {
        const analytic = getAnalyticByPageId.value(pageId);
        if (analytic && (!analytic.items || analytic.items.length === 0)) {
            const tempCurrentAnalytic = currentAnalytic.value;
            currentAnalytic.value = analytic;
            fetchAnalyticData(currentPeriod.value).then(() => {
                currentAnalytic.value = tempCurrentAnalytic;
            });
        }
    }

    function updateAllAnalyticsForPeriod(period: DateRange): void {
        if (!analyticList.value || analyticList.value.length === 0) {
            return;
        }

        analyticList.value = analyticList.value.map((analytic) => {
            const pageId = analytic.pageId;

            const items = analyticDashboardMock.map((item) => {
                const value = generateConsistentData(pageId, period, item.key);
                const lastPeriodValue = generateConsistentData(pageId + '-prev', period, item.key);
                const percent = Math.round(((value - lastPeriodValue) / lastPeriodValue) * 100);

                return {
                    ...item,
                    value,
                    percent,
                    lastPeriod: lastPeriodValue,
                };
            });

            const visitorsItem = items.find((item) => item.key === 'visitors');
            const sessionsItem = items.find((item) => item.key === 'sessions');
            const addToCartRateItem = items.find((item) => item.key === 'addToCartRate');
            const productViewsRateItem = items.find((item) => item.key === 'productViewsRate');

            const newSalesData = analyticChartMock.map((_, index) => {
                const date = new Date();
                date.setDate(date.getDate() - (analyticChartMock.length - 1 - index));

                const seed = `${pageId}-${period.alias}-sales-${index}`;
                const random = seededRandom(seed);
                const value = Math.round(50 + random * 200);

                return {
                    date: date.toISOString().split('T')[0],
                    value,
                };
            });

            const graphData = {
                sales: newSalesData,
            };

            const updatedAnalytic = {
                ...analytic,
                visitors: visitorsItem?.value || generateConsistentData(pageId, period, 'visitors'),
                sessions: sessionsItem?.value || generateConsistentData(pageId, period, 'sessions'),
                addToCartRate: addToCartRateItem?.value || generateConsistentData(pageId, period, 'addToCartRate'),
                productViewsRate:
                    productViewsRateItem?.value || generateConsistentData(pageId, period, 'productViewsRate'),
                items,
                graphData,
                updated_at: new Date().toISOString(),
            };

            return updatedAnalytic;
        });

        if (currentAnalytic.value) {
            const updatedCurrentAnalytic = analyticList.value.find((a) => a.id === currentAnalytic.value!.id);
            if (updatedCurrentAnalytic) {
                currentAnalytic.value = updatedCurrentAnalytic;
                items.value = updatedCurrentAnalytic.items;
                graphData.value = updatedCurrentAnalytic.graphData;
            }
        }
    }

    return {
        items,
        currentPeriod,
        isLoading,
        error,
        comparisonMode,
        graphData,
        activeDataKey,
        analyticList,
        currentAnalytic,

        queryValue,
        statusFilter,
        typeFilter,
        currentPage,
        itemsPerPage,

        getItemByKey,
        getAnalyticByPageId,
        getAnalyticById,
        formattedSalesData,
        formattedComparisonData,
        totalRevenue,
        activeItemData,

        filteredAnalytics,
        paginatedAnalytics,
        totalPages,
        totalAnalytics,

        fetchAnalyticData,
        updateDateRange,
        setComparisonMode,
        fetchComparisonData,
        setActiveDataKey,
        fetchPageAnalytics,
        setCurrentAnalytic,
        updateCurrentAnalyticData,
        loadAnalyticDataInstantly,
        preloadAnalyticData,
        generateConsistentData,
        updateAllAnalyticsForPeriod,

        setQueryValue,
        setStatusFilter,
        setTypeFilter,
        setCurrentPage,
        goToPreviousPage,
        goToNextPage,
        updateAnalyticStatus,
    };
});
