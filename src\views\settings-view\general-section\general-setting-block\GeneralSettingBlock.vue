<script setup lang="ts">
import { Icon, InlineStack, BlockStack, Button } from '@ownego/polaris-vue';
import DomainIcon from '@shopify/polaris-icons/DomainIcon.svg';
import EditIcon from '@shopify/polaris-icons/EditIcon.svg';
import LockIcon from '@shopify/polaris-icons/LockIcon.svg';
import PersonIcon from '@shopify/polaris-icons/PersonIcon.svg';
import { useI18n } from 'vue-i18n';

import { settingsLocaleVars } from '@/locales/settings';

const { t } = useI18n();
</script>

<template>
    <s-grid gridTemplateColumns="@container (inline-size <= 700px) 1fr, 2fr 5fr" gap="base">
        <s-box>
            <s-heading>{{ t(settingsLocaleVars.generalTitle) }}</s-heading>
            <s-paragraph>{{ t(settingsLocaleVars.generalDescription) }}</s-paragraph>
        </s-box>
        <s-section :accessibilityLabel="t(settingsLocaleVars.storeInformation)">
            <s-box padding="base" border="base" borderStyle="solid" borderRadius="base">
                <s-grid gap="base">
                    <s-stack direction="inline" justifyContent="space-between">
                        <InlineStack alignItems="center" :gap="400">
                            <div class="icon-container">
                                <Icon :source="PersonIcon" />
                            </div>
                            <BlockStack :gap="'050'">
                                <div>
                                    <InlineStack>
                                        <div class="text-container-bold">
                                            {{ t(settingsLocaleVars.storeOwner) }}
                                        </div>
                                        <div class="text-container">: easy-shop</div>
                                    </InlineStack>
                                </div>
                                <div class="text-container"><Text as="span"><EMAIL></Text></div>
                            </BlockStack>
                        </InlineStack>
                    </s-stack>
                    <s-divider></s-divider>
                    <s-stack direction="inline" justifyContent="space-between">
                        <InlineStack alignItems="center" :gap="400">
                            <div class="icon-container">
                                <Icon :source="DomainIcon" />
                            </div>
                            <BlockStack :gap="'050'">
                                <div>
                                    <InlineStack>
                                        <div class="text-container-bold">
                                            {{ t(settingsLocaleVars.shopifyDomain) }}
                                        </div>
                                        <div class="text-container">: easy-shop</div>
                                    </InlineStack>
                                </div>
                                <div class="text-container"><Text as="span">easy-shop.myshopify.com</Text></div>
                            </BlockStack>
                        </InlineStack>
                    </s-stack>
                    <s-divider></s-divider>
                    <s-stack direction="inline" justifyContent="space-between">
                        <InlineStack alignItems="center" :gap="400">
                            <div class="icon-container">
                                <Icon :source="LockIcon" />
                            </div>
                            <BlockStack :gap="'050'" :alignItems="'center'">
                                <div>
                                    <InlineStack>
                                        <div class="text-container-bold">
                                            {{ t(settingsLocaleVars.storefrontPassword) }}
                                        </div>
                                    </InlineStack>
                                </div>
                                <div class="text-container">
                                    <Text as="span">123456</Text>
                                </div>
                            </BlockStack>
                        </InlineStack>
                        <Button
                            :icon="EditIcon"
                            variant="tertiary"
                            :accessibilityLabel="t(settingsLocaleVars.editStoreName)"
                            class="text-container-button"
                        ></Button>
                    </s-stack>
                    <div>
                        {{ t(settingsLocaleVars.storefrontPasswordDescription) }}
                        <a class="text-container-link"> {{ t(settingsLocaleVars.turnOffPassword) }}</a
                        >.
                    </div>
                </s-grid>
            </s-box>
        </s-section>
    </s-grid>
</template>

<style scoped lang="scss">
.icon-container {
    padding: 9px;
}
.text-container {
    font-weight: 400;
    font-size: 13px;
}
.text-container-bold {
    font-weight: 600;
    font-size: 13px;
}
.text-container-button {
    margin-top: 5px;
    height: 20px;
}
.text-container-link {
    color: var(--m-shared-base);
}
</style>
