<script setup lang="ts">
import { AppProvider } from '@ownego/polaris-vue';
import locales from '@ownego/polaris-vue/dist/locales/en.json';
import { useAppStore } from '@stores/appStore';
import { storeToRefs } from 'pinia';
import { onMounted } from 'vue';
import { RouterView } from 'vue-router';

import { LoadingOverlay } from '@/components/loading-overlay';
import { SettingModals } from '@/layouts/modal/setting-modal';

const appStore = useAppStore();
const { isAppLoading } = storeToRefs(appStore);

onMounted(() => {
    setTimeout(() => {
        appStore.setIsAppLoading(false);
    }, 1000);
});
</script>

<template>
    <LoadingOverlay v-if="isAppLoading">
        <s-spinner accessibilityLabel="Loading" size="large-100" />
    </LoadingOverlay>
    <AppProvider v-else :i18n="locales">
        <RouterView />
        <SettingModals />
    </AppProvider>
</template>
