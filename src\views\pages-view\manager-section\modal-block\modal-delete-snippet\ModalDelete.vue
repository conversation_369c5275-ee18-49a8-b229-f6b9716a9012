<script setup lang="ts">
import { ModalBase } from '@components/modal-base';
import { Box } from '@ownego/polaris-vue';
import { useI18n } from 'vue-i18n';

import { pagesLocaleVars } from '@/locales/pages';

const { t } = useI18n();
</script>
<template>
    <ModalBase
        :id="'delete-page-modal'"
        :title="t(pagesLocaleVars.modalDeleteTitle)"
        :primary-action="t(pagesLocaleVars.modalDeleteConfirm)"
        :destructive="true"
    >
        <Box :padding="'400'">
            <p>{{ t(pagesLocaleVars.modalDeleteMessage) }}</p>
        </Box>
    </ModalBase>
</template>

<style scoped lang="scss"></style>
