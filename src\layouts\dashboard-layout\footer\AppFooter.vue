<script lang="ts" setup>
import Info from '@assets/svgs/info.svg';
import { Box, InlineStack } from '@ownego/polaris-vue';
</script>
<template>
    <footer class="footer">
        <Box :padding-block-start="800" :padding-block-end="800">
            <InlineStack :align="'center'" :gap="150">
                <Info />
                <Box>Learn more about at <a href="" target="_blank">matestore.io</a></Box>
            </InlineStack>
        </Box>
    </footer>
</template>
<style scoped lang="scss">
.footer {
    a {
        color: var(--m-blue-base);
        text-decoration: none;

        &:active {
            color: var(--m-blue-base);
        }
    }
}
</style>
