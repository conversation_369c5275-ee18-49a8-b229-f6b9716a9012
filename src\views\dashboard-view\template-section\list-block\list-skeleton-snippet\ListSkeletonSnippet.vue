<script lang="ts" setup>
import { BlockStack, Box, Card, InlineGrid, InlineStack, SkeletonThumbnail, Spinner } from '@ownego/polaris-vue';

defineProps<{
    showSpinner?: boolean;
}>();
</script>

<template>
    <div class="skeleton-overlay">
        <InlineGrid columns="3" gap="400">
            <Card v-for="i in 3" :key="i" padding="300">
                <div class="skeleton-card">
                    <div class="skeleton-image">
                        <SkeletonThumbnail />
                    </div>
                    <Box paddingBlockStart="300">
                        <BlockStack gap="150">
                            <div class="skeleton-title">
                                <SkeletonThumbnail />
                            </div>
                            <InlineStack :align="'space-between'">
                                <InlineStack gap="100">
                                    <div class="skeleton-price">
                                        <SkeletonThumbnail />
                                    </div>
                                </InlineStack>
                                <div class="skeleton-button">
                                    <SkeletonThumbnail />
                                </div>
                            </InlineStack>
                        </BlockStack>
                    </Box>
                </div>
            </Card>
        </InlineGrid>
        <div class="spinner-overlay">
            <Spinner accessibilityLabel="Loading" size="large" />
        </div>
    </div>
</template>

<style scoped lang="scss">
.skeleton-card {
    position: relative;
}

.skeleton-image {
    .Polaris-SkeletonThumbnail {
        width: 100%;
        height: 281px;
        border-radius: 8px;
    }
}

.skeleton-button {
    .Polaris-SkeletonThumbnail {
        width: 61px;
        height: 28px;
        border-radius: 8px;
    }
}

.skeleton-title {
    .Polaris-SkeletonThumbnail {
        width: 36px;
        height: 16px;
        border-radius: 8px;
    }
}

.skeleton-price {
    margin-top: 6px;

    .Polaris-SkeletonThumbnail {
        width: 68px;
        height: 16px;
        border-radius: 8px;
    }
}

.spinner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: var(--m-overlay-color);
    z-index: 20;
    width: 100%;
    height: 366px;
    border-radius: 8px;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }

    100% {
        background-position: 200% 0;
    }
}

@media (max-width: 768px) {
    .skeleton-overlay {
        :deep(.Polaris-InlineGrid) {
            grid-template-columns: repeat(2, 1fr);
        }
    }
}

@media (max-width: 480px) {
    .skeleton-overlay {
        :deep(.Polaris-InlineGrid) {
            grid-template-columns: 1fr;
        }
    }
}
</style>
