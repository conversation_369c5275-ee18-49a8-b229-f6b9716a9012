<script setup lang="ts">
import { dashboardLocaleVars } from '@locales/dashboard';
import { ref, onMounted, computed } from 'vue';
import { useI18n } from 'vue-i18n';

import { ListBlock } from './list-block';
import { TabBlock } from './tab-block';
import { tabs } from './tab-block/configs';

const { t } = useI18n();
const activeTab = ref('all');
const searchQuery = ref('');
const listBlockRef = ref<InstanceType<typeof ListBlock>>();
const isInitialLoading = ref(true);
const isListLoading = ref(false);

onMounted(() => {
    setTimeout(() => {
        isInitialLoading.value = false;
    }, 1000);
});

const isNextEnabled = computed(() => {
    const result = !isInitialLoading.value && !isListLoading.value;
    return result;
});

const handleLoadingChange = (loading: boolean) => {
    isListLoading.value = loading;
};
const handleTabChange = (tabId: string) => {
    activeTab.value = tabId;
    setTimeout(() => {
        if (listBlockRef.value && typeof listBlockRef.value.clearSelections === 'function') {
            listBlockRef.value.clearSelections();
        }
    }, 0);
};

const handleSearchChange = (query: string) => {
    searchQuery.value = query;
};

const handleNext = () => {
    if (!isNextEnabled.value) {
        return;
    }
    const currentIndex = tabs.findIndex((tab) => tab.panelID === activeTab.value);
    const nextIndex = (currentIndex + 1) % tabs.length;
    activeTab.value = tabs[nextIndex].panelID;
};

const handleModalOpen = () => {
    isInitialLoading.value = true;
    setTimeout(() => {
        isInitialLoading.value = false;
    }, 1000);
};
</script>
<template>
    <ui-modal id="my-modal" variant="large" @open="handleModalOpen">
        <div class="template-section">
            <div class="tab-section">
                <TabBlock :active-tab="activeTab" @tab-change="handleTabChange" @search-change="handleSearchChange" />
            </div>
            <div class="list-section">
                <ListBlock
                    ref="listBlockRef"
                    :filter-tab="activeTab"
                    :search-query="searchQuery"
                    :initial-loading="isInitialLoading"
                    @loading-change="handleLoadingChange"
                />
            </div>
        </div>
        <ui-title-bar :title="t(dashboardLocaleVars.templatesTitle)">
            <button
                type="button"
                variant="primary"
                :disabled="!isNextEnabled"
                @click="handleNext"
                :class="{ disabled: !isNextEnabled }"
            >
                {{ t(dashboardLocaleVars.nextButton) }}
            </button>
        </ui-title-bar>
    </ui-modal>
</template>
<style scoped lang="scss">
.template-section {
    display: flex;
    flex-direction: column;
}

.tab-section {
    z-index: 20;
}

.list-section {
    position: relative;
}

button.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}
</style>
