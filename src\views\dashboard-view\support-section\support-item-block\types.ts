import { type PropType } from 'vue';

export interface ButtonProps {
    content: string;
    onClick?: () => void;
    url?: string;
    external?: boolean;
    variant?: 'primary' | 'secondary' | 'tertiary' | 'plain';
}

export const SupportItemBlockProps = {
    title: {
        type: String,
        required: true,
    },
    description: {
        type: String,
        required: true,
    },
    icon: {
        type: [String, Object] as PropType<any>,
        required: true,
    },
    primaryButton: {
        type: [Object, null] as PropType<ButtonProps | null>,
        required: false,
        default: null,
    },
    secondaryButton: {
        type: [Object, null] as PropType<ButtonProps | null>,
        required: false,
        default: null,
    },
};
