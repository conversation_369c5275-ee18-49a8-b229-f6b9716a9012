<script setup lang="ts">
import { Card } from '@ownego/polaris-vue';
import { ref, computed, onMounted, watch } from 'vue';

import { useAnalyticStore } from '@/stores/analyticStore';

import { resourceName, tableHeadings, PageStatusOptions } from './configs';
import { FilterDateBlock } from './filter-date-block';
import { ListBlock } from './list-block';
import { EmptyStateSnippet } from './list-block/empty-state-snippet';
import { PaginateBlock } from './paginate-block';
import { SkeletonBlock } from './skeleton-block';
import { useFilters } from './utils';

const analyticStore = useAnalyticStore();

onMounted(async () => {
    if (analyticStore.analyticList.length === 0) {
        analyticStore.isLoading = true;

        try {
            await analyticStore.fetchPageAnalytics();
        } catch (error) {
            console.error('Failed to load analytics:', error);
        } finally {
            analyticStore.isLoading = false;
        }
    }
});

const selected = ref(0);
const { queryValue, status, handleStatus, appliedFilters } = useFilters();

const listBlockRef = ref();

watch(queryValue, (newValue) => {
    analyticStore.setQueryValue(newValue);
});

watch(status, (newStatus) => {
    analyticStore.setStatusFilter(newStatus);
});

watch(
    () => analyticStore.currentPeriod,
    async (newPeriod) => {
        if (newPeriod && analyticStore.analyticList.length > 0) {
            analyticStore.updateAllAnalyticsForPeriod(newPeriod);

            if (analyticStore.currentAnalytic) {
                await analyticStore.fetchAnalyticData(newPeriod);
            }
        }
    },
    { deep: true },
);

watch(selected, (newSelected) => {
    const selectedOption = PageStatusOptions[newSelected];
    if (selectedOption?.content !== 'All') {
        analyticStore.setStatusFilter([selectedOption.content]);
    } else {
        analyticStore.setStatusFilter([]);
    }
});

const filteredData = computed(() => analyticStore.filteredAnalytics);
const paginatedData = computed(() => analyticStore.paginatedAnalytics);
const currentPage = computed(() => analyticStore.currentPage);
const totalPages = computed(() => analyticStore.totalPages);

const goToPreviousPage = () => {
    analyticStore.goToPreviousPage();
};

const goToNextPage = () => {
    if (currentPage.value < totalPages.value) {
        analyticStore.goToNextPage();
    }
};

const handleFiltersSelect = (index: number) => {
    selected.value = index;
    analyticStore.setCurrentPage(1);
};

const handleFiltersQueryChange = (value: string) => {
    analyticStore.setQueryValue(value);
    analyticStore.setCurrentPage(1);
};

const updateStatus = (ids: string[], newStatus: string) => {
    ids.forEach((id) => {
        analyticStore.updateAnalyticStatus(id, newStatus);
    });
};

watch(paginatedData, () => {
    listBlockRef.value?.clearSelection();
});
</script>

<template>
    <SkeletonBlock v-if="analyticStore.isLoading" />

    <template v-else>
        <div class="filter-date-container" style="margin-bottom: 16px">
            <FilterDateBlock />
        </div>

        <Card v-if="analyticStore.analyticList.length > 0" padding="none">
            <ListBlock
                ref="listBlockRef"
                :status="status"
                :appliedFilters="appliedFilters"
                :Page="paginatedData"
                :resourceName="resourceName"
                :tableHeadings="tableHeadings"
                :PageStatusOptions="PageStatusOptions"
                :selected="selected"
                :handleFiltersSelect="handleFiltersSelect"
                :update-status="updateStatus"
                :handleFiltersQueryChange="handleFiltersQueryChange"
                :handleStatus="handleStatus"
            />
            <PaginateBlock
                :currentPage="currentPage"
                :totalPages="totalPages"
                :totalItems="filteredData.length"
                :startItem="(currentPage - 1) * analyticStore.itemsPerPage + 1"
                :endItem="Math.min(currentPage * analyticStore.itemsPerPage, filteredData.length)"
                :goToPreviousPage="goToPreviousPage"
                :goToNextPage="goToNextPage"
            />
        </Card>

        <Card v-else padding="none">
            <EmptyStateSnippet />
        </Card>
    </template>
</template>

<style scoped lang="scss"></style>
