<script setup lang="ts">
import { IndexTableRow, IndexTableCell, Text, BlockStack, Badge, Box } from '@ownego/polaris-vue';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';

import { pagesLocaleVars } from '@/locales/pages';

import type { ListRowSnippetProps } from './types';

const { t } = useI18n();
const router = useRouter();
const props = defineProps<ListRowSnippetProps>();


const getTranslatedStatus = computed(() => {
    return props.Page.pageData.status === 'Published'
        ? t(pagesLocaleVars.pageStatusPublished)
        : props.Page.pageData.status === 'Unpublished'
            ? t(pagesLocaleVars.pageStatusUnpublished)
            : props.Page.pageData.status;
});

// Computed properties cho các trường analytics
const getAddToCartRate = computed(() => {
    return `${props.Page.addToCartRate}%`;
});

const getProductViewsRate = computed(() => {
    return `${props.Page.productViewsRate}%`;
});

const getVisitors = computed(() => {
    return props.Page.visitors.toLocaleString();
});

const getSessions = computed(() => {
    return props.Page.sessions.toLocaleString();
});

const getSales = computed(() => {
    return `$${props.Page.Sales.toLocaleString()}`;
});

const getConversionRate = computed(() => {
    return `${props.Page.Conversion_rate}%`;
});

const getABTesting = computed(() => {
    return '--';
});

const navigateToAnalytics = (event: Event) => {
    event.stopPropagation();
    router.push(`/analytic/Detail/${props.Page.pageId}`);
};
</script>

<template>
    <IndexTableRow :id="String(Page.id)" :key="Page.id" :position="index" :selected="selected">
        <IndexTableCell :class="'cell__wrapper firt-cell'" @click="navigateToAnalytics">
            <BlockStack>
                <Box>
                    <Text variant="bodyMd" fontWeight="medium" as="span" class="clickable-text">{{ Page.pageData.title
                    }}</Text>
                </Box>
                <Box>
                    <Text as="span" :tone="'subdued'" font-weight="regular" class="clickable-text">{{ Page.pageData.url
                    }}</Text>
                </Box>
            </BlockStack>
        </IndexTableCell>
        <IndexTableCell :class="'cell__wrapper second-cell'" @click="navigateToAnalytics">
            <div class="badge__wrapper">
                <div :class="`badge__wrapper__${Page.pageData.status}`">
                    <Badge :progress="Page.pageData.status === 'Published' ? 'complete' : 'incomplete'"
                        class="clickable-badge">
                        {{ getTranslatedStatus }}
                    </Badge>
                </div>
            </div>
        </IndexTableCell>
        <IndexTableCell :class="'cell__wrapper third-cell'" @click="navigateToAnalytics">
            <Text as="span" :alignment="'center'" numeric class="clickable-text">
                {{ getAddToCartRate }}
            </Text>
        </IndexTableCell>
        <IndexTableCell :class="'cell__wrapper fourth-cell'" @click="navigateToAnalytics">
            <Text as="span" :alignment="'center'" numeric class="clickable-text">
                {{ getProductViewsRate }}
            </Text>
        </IndexTableCell>
        <IndexTableCell :class="'cell__wrapper fifth-cell'" @click="navigateToAnalytics">
            <Text as="span" :alignment="'center'" numeric class="clickable-text">
                {{ getVisitors }}
            </Text>
        </IndexTableCell>
        <IndexTableCell :class="'cell__wrapper sixth-cell'" @click="navigateToAnalytics">
            <Text as="span" :alignment="'center'" numeric class="clickable-text">
                {{ getSessions }}
            </Text>
        </IndexTableCell>
        <IndexTableCell :class="'cell__wrapper seventh-cell'" @click="navigateToAnalytics">
            <Text as="span" :alignment="'center'" numeric class="clickable-text">
                {{ getSales }}
            </Text>
        </IndexTableCell>
        <IndexTableCell :class="'cell__wrapper eighth-cell'" @click="navigateToAnalytics">
            <Text as="span" :alignment="'center'" numeric class="clickable-text">
                {{ getConversionRate }}
            </Text>
        </IndexTableCell>
        <IndexTableCell :class="'cell__wrapper ninth-cell'" @click="navigateToAnalytics">
            <Text as="span" :alignment="'center'" class="clickable-text">
                {{ getABTesting }}
            </Text>
        </IndexTableCell>

    </IndexTableRow>
</template>

<style scoped lang="scss">
.cell__wrapper {
    height: 40px;
}

.clickable-text {
    cursor: pointer;
    display: inline-block;
}

.clickable-badge {
    cursor: pointer;
}

.firt-cell {
    width: 20%;
}

.second-cell {
    width: 8%;
}

.third-cell {
    width: 10%;
}

.fourth-cell {
    width: 10%;
}

.fifth-cell {
    width: 8%;
}

.sixth-cell {
    width: 8%;
}

.seventh-cell {
    width: 12%;
}

.eighth-cell {
    width: 10%;
}

.ninth-cell {
    width: 14%;
}

.badge__wrapper {
    &__Published {
        .Polaris-Badge {
            background-color: var(--m-success-base-20);
            color: var(--m-success-base-30);

            svg {
                fill: var(--m-success-base-30);
            }
        }
    }

    &__unpublished {
        .Polaris-Badge {
            background-color: var(--m-overlay-color-2);
        }
    }
}
</style>
