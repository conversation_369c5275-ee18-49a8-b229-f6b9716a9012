<script setup lang="ts">
import { dashboardLocaleVars } from '@locales/dashboard';
import { BlockStack } from '@ownego/polaris-vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const handleTurnOnShopifyEditor = () => {
    const urlSearchParams = new URLSearchParams(window.location.search);
    const params = Object.fromEntries(urlSearchParams.entries());
    const shop = params.shop || '';

    const shopName = shop.split('.')[0] || 'ngothanhcuong404s';

    const themeId = '148941209837';

    const adminUrl = `https://admin.shopify.com/store/${shopName}/themes/${themeId}/editor?context=apps`;

    window.open(adminUrl, '_blank');
};
</script>

<template>
    <s-banner :heading="t(dashboardLocaleVars.bannerHeading)" :tone="'warning'" :dismissible="true">
        <BlockStack :gap="200">
            <s-box>
                <s-text class="banner-description" variant="bodyMd" tone="subdued">
                    {{ t(dashboardLocaleVars.bannerDescription) }}
                    <a href="https://shopify.dev/docs/apps/build/online-store/theme-app-extensions">
                        {{ t(dashboardLocaleVars.bannerLearnMore) }}
                    </a>
                </s-text>
            </s-box>
            <s-box>
                <s-button @click="handleTurnOnShopifyEditor">{{ t(dashboardLocaleVars.bannerButtonText) }}</s-button>
            </s-box>
        </BlockStack>
    </s-banner>
</template>

<style scoped lang="scss">
.banner-description {
    a {
        color: var(--m-blue-base);
    }
}
</style>
