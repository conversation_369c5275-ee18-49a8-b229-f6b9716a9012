const makeFormsLocaleVars = (text: string) => `FORMS_${text}`;

const formsLocaleVars = {
    forms: makeFormsLocaleVars('FORMS'),
    submissions: makeFormsLocaleVars('SUBMISSIONS'),

    formsManagerTitle: makeFormsLocaleVars('MANAGER_TITLE'),
    formsStatusLabel: makeFormsLocaleVars('STATUS_LABEL'),
    formsStatusAll: makeFormsLocaleVars('STATUS_ALL'),
    formsStatusPublished: makeFormsLocaleVars('STATUS_PUBLISHED'),
    formsStatusUnpublished: makeFormsLocaleVars('STATUS_UNPUBLISHED'),

    formsTableFormId: makeFormsLocaleVars('TABLE_FORM_ID'),
    formsTableFormTitle: makeFormsLocaleVars('TABLE_FORM_TITLE'),
    formsTableStatus: makeFormsLocaleVars('TABLE_STATUS'),
    formsTableSubmissions: makeFormsLocaleVars('TABLE_SUBMISSIONS'),
    formsTableLastUpdated: makeFormsLocaleVars('TABLE_LAST_UPDATED'),

    formsActionsPublish: makeFormsLocaleVars('ACTIONS_PUBLISH'),
    formsActionsUnpublish: makeFormsLocaleVars('ACTIONS_UNPUBLISH'),
    formsActionsEdit: makeFormsLocaleVars('ACTIONS_EDIT'),
    formsActionsView: makeFormsLocaleVars('ACTIONS_VIEW'),
    formsActionsDelete: makeFormsLocaleVars('ACTIONS_DELETE'),
    formsActionsDuplicate: makeFormsLocaleVars('ACTIONS_DUPLICATE'),

    formsEmptyTitle: makeFormsLocaleVars('EMPTY_TITLE'),
    formsEmptyMessage: makeFormsLocaleVars('EMPTY_MESSAGE'),
    formsEmptyCreateNew: makeFormsLocaleVars('EMPTY_CREATE_NEW'),

    formsSearchPlaceholder: makeFormsLocaleVars('SEARCH_PLACEHOLDER'),

    submissionsTitle: makeFormsLocaleVars('SUBMISSIONS_TITLE'),
    submissionsStatusLabel: makeFormsLocaleVars('SUBMISSIONS_STATUS_LABEL'),
    submissionsStatusAll: makeFormsLocaleVars('SUBMISSIONS_STATUS_ALL'),
    submissionsStatusRead: makeFormsLocaleVars('SUBMISSIONS_STATUS_READ'),
    submissionsStatusUnread: makeFormsLocaleVars('SUBMISSIONS_STATUS_UNREAD'),
    submissionsLearnMore: makeFormsLocaleVars('SUBMISSIONS_LEARN_MORE'),
    submissionsEmptyTitle: makeFormsLocaleVars('SUBMISSIONS_EMPTY_TITLE'),
    submissionsEmptyMessage: makeFormsLocaleVars('SUBMISSIONS_EMPTY_MESSAGE'),
    submissionsGoBack: makeFormsLocaleVars('SUBMISSIONS_GO_BACK'),
    submissionsSearchingIn: makeFormsLocaleVars('SUBMISSIONS_SEARCHING_IN'),
    submissionsSortAscending: makeFormsLocaleVars('SUBMISSIONS_SORT_ASCENDING'),
    submissionsSortDescending: makeFormsLocaleVars('SUBMISSIONS_SORT_DESCENDING'),

    submissionsTableFormId: makeFormsLocaleVars('SUBMISSIONS_TABLE_FORM_ID'),
    submissionsTableEmail: makeFormsLocaleVars('SUBMISSIONS_TABLE_EMAIL'),
    submissionsTableName: makeFormsLocaleVars('SUBMISSIONS_TABLE_NAME'),
    submissionsTableStatus: makeFormsLocaleVars('SUBMISSIONS_TABLE_STATUS'),
    submissionsTableLastUpdated: makeFormsLocaleVars('SUBMISSIONS_TABLE_LAST_UPDATED'),

    paginationPrevious: makeFormsLocaleVars('PAGINATION_PREVIOUS'),
    paginationNext: makeFormsLocaleVars('PAGINATION_NEXT'),
    paginationOf: makeFormsLocaleVars('PAGINATION_OF'),
};

export default formsLocaleVars;
