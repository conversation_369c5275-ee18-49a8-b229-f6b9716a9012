<script setup lang="ts">
import { BlockStack, Box, Button, Card, Icon, InlineStack, Text, Tooltip } from '@ownego/polaris-vue';
import XIcon from '@shopify/polaris-icons/XIcon.svg';
import { ref } from 'vue';

const isVisible = ref(true);

const handleDismiss = () => {
    isVisible.value = false;
};

const handleExplore = () => {
    window.open('https://www.shopify.com/editions', '_blank');
};
</script>

<template>
    <div v-if="isVisible" class="shopify-editions">
        <Card padding="0">
            <Box>
                <div class="shopify-editions__background">
                    <Box padding="400">
                        <div class="shopify-editions__content">
                            <BlockStack gap="400">
                                <BlockStack gap="200">
                                    <Text as="h2" variant="headingMd">Shopify Editions | Summer '25</Text>
                                    <Text as="span" variant="headingMd"> New features, good vibes </Text>
                                </BlockStack>

                                <InlineStack>
                                    <Button @click="handleExplore">
                                        <Text as="span" variant="bodySm" font-weight="medium">
                                            Dive into 150+ updates
                                        </Text>
                                    </Button>
                                </InlineStack>
                            </BlockStack>
                        </div>
                        <Tooltip content="Dismiss" dismissOnMouseOut :preferredPosition="'above'">
                            <div class="shopify-editions__close">
                                <Button
                                    class="shopify-editions__button"
                                    @click="handleDismiss"
                                    :padding="0"
                                    aria-label="Dismiss"
                                    data-polaris-tooltip-activator="true"
                                >
                                    <Icon :source="XIcon" color="base" />
                                </Button>
                            </div>
                        </Tooltip>
                    </Box>
                </div>
            </Box>
        </Card>
    </div>
</template>

<style scoped lang="scss">
.shopify-editions {
    position: relative;

    &__background {
        position: relative;
        background-image: url('https://cdn.shopify.com/b/shopify-guidance-dashboard-public/6eb0tpqspkeprmdmob2xjlu7qsfi.png');
        background-size: cover;
        background-position: center;
        border-radius: var(--p-border-radius-200);
        overflow: hidden;
    }

    &__content {
        color: #fff;
        max-width: 600px;
    }

    &__button {
        background-color: #fff9;
        color: var(--p-color-icon-secondary);
        box-shadow: none;

        padding: 0;
        &:hover {
            background-color: var(--m-surface-background);
        }
    }

    &__close {
        position: absolute;
        top: 16px;
        right: 16px;
        z-index: 1;
        opacity: 0;
        transition: opacity 0.2s ease-in-out;
    }

    &:hover &__close {
        opacity: 1;
    }
}

@media (max-width: 767px) {
    .shopify-editions {
        &__content {
            max-width: 100%;
        }
    }
}
</style>
