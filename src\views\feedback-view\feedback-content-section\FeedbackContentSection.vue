<script setup lang="ts">
import FreeGift from '@assets/svgs/free-gift.svg';
import { feedbackLocaleVars } from '@locales/feedback';
import { BlockStack, Button, Text, InlineStack, Card, Box } from '@ownego/polaris-vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
</script>
<template>
    <Card padding="none">
        <div class="feedback-content-section">
            <BlockStack :align="'center'" gap="500">
                <InlineStack :align="'center'" gap="400">
                    <BlockStack>
                        <Box :padding-block-start="1000">
                            <InlineStack :align="'center'">
                                <img
                                    src="/src/assets/images/ThankyouImage.png"
                                    alt="Team Illustration"
                                    style="max-width: 226px"
                                />
                            </InlineStack>
                        </Box>
                        <BlockStack :align="'center'" gap="100">
                            <InlineStack :align="'center'" gap="100">
                                <Text as="h2" variant="headingMd">{{ t(feedbackLocaleVars.thankYou) }}</Text>
                            </InlineStack>
                            <Box max-width="450px">
                                <Text as="p" variant="bodyMd" tone="subdued" alignment="center">
                                    {{ t(feedbackLocaleVars.happyToHear) }}
                                </Text>
                            </Box>
                        </BlockStack>
                    </BlockStack>
                </InlineStack>
                <InlineStack :align="'center'">
                    <div style="width: 620px">
                        <Card padding="none">
                            <InlineStack :align="'start'">
                                <FreeGift />
                                <Box :padding="'400'" width="500px">
                                    <BlockStack gap="200">
                                        <Text as="h3" variant="bodyMd" fontWeight="bold">
                                            {{ t(feedbackLocaleVars.freeGiftTitle) }}
                                        </Text>
                                        <Text as="p" variant="bodySm" tone="subdued">
                                            {{ t(feedbackLocaleVars.freeGiftDescription) }}
                                        </Text>
                                    </BlockStack>
                                </Box>
                            </InlineStack>
                        </Card>
                    </div>
                </InlineStack>
                <Box :padding-block-end="1600">
                    <InlineStack :align="'center'">
                        <Button variant="primary" size="medium">{{ t(feedbackLocaleVars.leaveReview) }}</Button>
                    </InlineStack>
                </Box>
            </BlockStack>
        </div>
    </Card>
</template>
<style scoped lang="scss"></style>
