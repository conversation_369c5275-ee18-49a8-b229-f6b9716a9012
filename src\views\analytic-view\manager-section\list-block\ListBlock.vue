<script setup lang="ts">
import { Divider, IndexFilters, IndexTable, useIndexResourceState, useSetIndexFiltersMode } from '@ownego/polaris-vue';
import { ref, computed, h, resolveComponent } from 'vue';
import { useI18n } from 'vue-i18n';

import { pagesLocaleVars } from '@/locales/pages';

import { filterChoices } from '../configs';
import { useFilters } from '../utils';
import { ListRowSnippet } from './list-row-snippet';

const { t } = useI18n();
const sortSelected = ref(['order asc']);
const { mode, setMode } = useSetIndexFiltersMode();
const props = defineProps<{
    Page: any[];
    resourceName: any;
    tableHeadings: any[];
    PageStatusOptions: any[];
    selected: number;
    status: string[];
    handleFiltersSelect: (_value: any) => void;
    handleStatus: (_value: string[]) => void;
    handleFiltersQueryChange: (_value: any) => void;
    appliedFilters: any[];
    updateStatus: (_ids: string[], _newStatus: string) => void;
}>();

const { queryValue, handleQueryValueRemove, handleFiltersClearAll } = useFilters();
const currentPageData = computed(() => props.Page);
const { selectedResources, allResourcesSelected, handleSelectionChange, clearSelection } =
    useIndexResourceState(currentPageData);

defineExpose({
    clearSelection,
});

const filters = computed(() => [
    {
        name: 'status',
        label: t(pagesLocaleVars.pageStatusLabel),
        filter: () =>
            h(resolveComponent('ChoiceList'), {
                title: t(pagesLocaleVars.pageStatusLabel),
                titleHidden: true,
                choices: filterChoices.map((choice) => ({
                    ...choice,
                    label:
                        choice.value === 'All'
                            ? t(pagesLocaleVars.pageStatusAll)
                            : choice.value === 'Published'
                                ? t(pagesLocaleVars.pageStatusPublished)
                                : choice.value === 'Unpublished'
                                    ? t(pagesLocaleVars.pageStatusUnpublished)
                                    : choice.label,
                })),
                modelValue: props.status || [],
                onChange: props.handleStatus,
                allowMultiple: true,
            }),
        shortcut: true,
    },
]);

const onHandleCancel = () => { };

const handleFiltersSort = (value: string[]) => {
    sortSelected.value = value;
};

const translatedSortOptions = computed<any[]>(() => [
    { label: t(pagesLocaleVars.pageTableTitle), value: 'order asc', directionLabel: 'Ascending' },
    { label: t(pagesLocaleVars.pageTableTitle), value: 'order desc', directionLabel: 'Descending' },
    { label: t(pagesLocaleVars.pageTableLastUpdated), value: 'customer asc', directionLabel: 'A-Z' },
    { label: t(pagesLocaleVars.pageTableLastUpdated), value: 'customer desc', directionLabel: 'Z-A' },
]);

const translatedStatusOptions = computed(() =>
    props.PageStatusOptions.map((option) => ({
        ...option,
        content:
            option.content === 'All'
                ? t(pagesLocaleVars.pageStatusAll)
                : option.content === 'Published'
                    ? t(pagesLocaleVars.pageStatusPublished)
                    : option.content === 'Unpublished'
                        ? t(pagesLocaleVars.pageStatusUnpublished)
                        : option.content,
    })),
);

const tableHeadingsList: any = [
    { title: 'Title' },
    { title: 'Status' },
    { title: 'Add to cart rate' },
    { title: 'Product views rate' },
    { title: 'Visitors' },
    { title: 'Sessions' },

];
</script>

<template>
    <div class="index-filters-container">
        <IndexFilters :sortOptions="translatedSortOptions" :sortSelected="sortSelected" :queryValue="queryValue"
            :queryPlaceholder="t(pagesLocaleVars.pageSearchPlaceholder)"
            :cancelAction="{ onAction: onHandleCancel, disabled: false, loading: false }"
            :tabs="translatedStatusOptions" :selected="selected" :filters="filters" :appliedFilters="appliedFilters"
            :mode="mode" @set-mode="setMode" @query-change="handleFiltersQueryChange"
            @query-clear="handleQueryValueRemove" @sort="handleFiltersSort" @select="handleFiltersSelect"
            @clear-all="handleFiltersClearAll" :can-create-new-view="false" />
    </div>
    <IndexTable :condensed="false" :resourceName="resourceName" :itemCount="Page.length"
        :selectedItemsCount="allResourcesSelected ? 'All' : selectedResources.length"
        @selection-change="handleSelectionChange" :headings="tableHeadingsList" :selectable="false">
        <ListRowSnippet v-for="Page in props.Page" :key="Page.id" :Page="Page" :index="Number(Page.id)"
            :selected="selectedResources.includes(String(Page.id))" />
    </IndexTable>

    <Divider borderColor="border" />
</template>

<style scoped lang="scss">
.index-filters-container {
    .Polaris-Text--root {
        color: var(--m-black-base);
    }
}
</style>
