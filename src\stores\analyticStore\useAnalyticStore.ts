import { defineStore } from 'pinia';
import { computed, ref, watch } from 'vue';

import type { Chart<PERSON><PERSON>, DateRange, DataPoint } from '@/views/analytic-view/analytic-detail-section/chart-block/types';

import { usePagesStore } from '@/stores/pagesStore';
import {
    analyticDashboardMock,
    analyticChartMock,
    mockPageAnalytics,
} from '@/views/analytic-view/analytic-detail-section/chart-block/config';

import type { PageAnalytic, PageWithAnalytics } from './types';

// Utility function để tạo data cố định dựa trên seed
function seededRandom(seed: string): number {
    let hash = 0;
    for (let i = 0; i < seed.length; i++) {
        const char = seed.charCodeAt(i);
        hash = (hash << 5) - hash + char;
        hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash) / 2147483647; // Normalize to 0-1
}

// Function để generate consistent data dựa trên pageId và period
function generateConsistentData(pageId: string, period: DateRange, dataType: string): any {
    const seed = `${pageId}-${period.alias}-${dataType}`;
    const random = seededRandom(seed);

    switch (dataType) {
        case 'visitors':
            return Math.floor(random * 5000) + 500; // 500-5500
        case 'sessions':
            return Math.floor(random * 8000) + 800; // 800-8800
        case 'addToCartRate':
            return Math.round((random * 10 + 1) * 10) / 10; // 1.0-11.0%
        case 'productViewsRate':
            return Math.round((random * 30 + 5) * 10) / 10; // 5.0-35.0%
        default:
            return Math.floor(random * 1000);
    }
}

export const useAnalyticStore = defineStore('analytic', () => {
    // Get pagesStore instance
    const pagesStore = usePagesStore();

    // Detail view state
    const items = ref<ChartItem[]>([]);
    const isLoading = ref<boolean>(false);
    const error = ref<string | null>(null);
    const activeDataKey = ref<string | null>(null);
    const analyticList = ref<PageAnalytic[]>([]);
    const currentAnalytic = ref<PageAnalytic | null>(null);

    watch(
        () => currentAnalytic.value?.items,
        (newItems) => {
            if (newItems && newItems.length > 0) {
                items.value = [...newItems]; 
            }
        },
        { immediate: true, deep: true },
    );

    // Manager view state
    const queryValue = ref<string>('');
    const statusFilter = ref<string[]>([]);
    const typeFilter = ref<string>('All');
    const currentPage = ref<number>(1);
    const itemsPerPage = ref<number>(6);

    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    const sevenDaysAgo = new Date(today);
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const currentPeriod = ref<DateRange>({
        alias: 'last7days',
        title: 'Last 7 days',
        period: {
            since: sevenDaysAgo,
            until: yesterday,
        },
    });

    const comparisonMode = ref<'previous_period' | 'previous_year' | 'custom'>('previous_period');

    const graphData = ref<{
        sales: { date: string; value: number }[];
        comparison?: { date: string; value: number }[];
    }>({
        sales: analyticChartMock,
    });

    // Getters
    const getItemByKey = computed(() => {
        return (key: string) => items.value.find((item) => item.key === key);
    });

    const getAnalyticByPageId = computed(() => {
        return (pageId: string) => analyticList.value.find((analytic) => analytic.pageId === pageId);
    });

    const formattedSalesData = computed((): DataPoint[] => {
        // Sử dụng trực tiếp dữ liệu từ graphData.sales để đảm bảo biểu đồ hiển thị đúng
        const result = graphData.value.sales.map((d) => ({
            date: new Date(d.date),
            value: d.value,
        }));
        console.log('formattedSalesData:', result);
        return result;
    });

    const formattedComparisonData = computed((): DataPoint[] | null => {
        if (!graphData.value.comparison) return null;

        // Sử dụng trực tiếp dữ liệu từ graphData.comparison
        return graphData.value.comparison.map((d) => ({
            date: new Date(d.date),
            value: d.value,
        }));
    });

    const totalRevenue = computed((): number => {
        const revenueItem = items.value.find((item) => item.key === 'offersRevenue');
        return revenueItem?.value || 0;
    });

    async function fetchAnalyticData(period: DateRange): Promise<void> {
        isLoading.value = true;
        error.value = null;

        try {
            const currentActiveKey = activeDataKey.value;

            await new Promise((resolve) => setTimeout(resolve, 200));

            if (!currentAnalytic.value) {
                console.warn('No current analytic set, cannot fetch data');
                isLoading.value = false;
                return;
            }
            if (currentAnalytic.value.items && currentAnalytic.value.items.length > 0) {
                items.value = currentAnalytic.value.items;
            } else {
                // Fallback: sử dụng dữ liệu mẫu cố định nếu không có items
                const pageId = currentAnalytic.value.pageId;
                const mockData = mockPageAnalytics.find(mock => mock.pageId === pageId);

                if (mockData && mockData.items.length > 0) {
                    items.value = mockData.items;
                } else {
                    // Fallback cuối cùng: tạo items mặc định
                    items.value = analyticDashboardMock.map(item => ({
                        ...item,
                        value: 0,
                        percent: 0,
                        lastPeriod: 0
                    }));
                }
            }

            currentPeriod.value = period;

            if (currentAnalytic.value.graphData && currentAnalytic.value.graphData.sales.length > 0) {
                graphData.value = currentAnalytic.value.graphData;
                console.log('Using existing graphData:', currentAnalytic.value.graphData);
            } else {
                const pageId = currentAnalytic.value.pageId;
                const daysInRange = Math.max(
                    Math.round(
                        (period.period.until.getTime() - period.period.since.getTime()) / (1000 * 60 * 60 * 24),
                    ) + 1,
                    7,
                );

                const newSalesData = Array.from({ length: daysInRange }, (_, i) => {
                    const date = new Date(period.period.since);
                    date.setDate(date.getDate() + i);

                    const seed = `${pageId}-${period.alias}-chart-${i}`;
                    const random = seededRandom(seed);

                    const trendFactor = (i / (daysInRange - 1)) * 0.5 + 0.5;
                    const value = Math.floor(random * 15 * trendFactor) + 5;

                    return {
                        date: date.toISOString().split('T')[0],
                        value,
                    };
                });

                graphData.value = {
                    ...graphData.value,
                    sales: newSalesData,
                };
            }

            if (currentActiveKey) {
                const keyExists = items.value.some((item) => item.key === currentActiveKey);
                if (keyExists) {
                    activeDataKey.value = currentActiveKey;
                } else if (items.value.length > 0) {
                    activeDataKey.value = items.value[0].key;
                }
            } else if (items.value.length > 0) {
                activeDataKey.value = items.value[0].key;
            }

            updateCurrentAnalyticData();
        } catch (err) {
            error.value = err instanceof Error ? err.message : 'Failed to fetch analytic data';
        } finally {
            isLoading.value = false;
        }
    }

    function updateDateRange(range: DateRange): void {
        currentPeriod.value = range;

        updateAllAnalyticsForPeriod(range);

        if (currentAnalytic.value && currentAnalytic.value.pageId) {
            fetchAnalyticData(range);
        }
    }

    function setComparisonMode(mode: 'previous_period' | 'previous_year' | 'custom'): void {
        comparisonMode.value = mode;
        fetchComparisonData();
    }

    async function fetchComparisonData(): Promise<void> {
        if (!currentPeriod.value) return;

        try {
            await new Promise((resolve) => setTimeout(resolve, 100));

            const period = currentPeriod.value;
            const daysInRange = Math.max(
                Math.round((period.period.until.getTime() - period.period.since.getTime()) / (1000 * 60 * 60 * 24)) + 1,
                7,
            );

            let startDate: Date;

            if (comparisonMode.value === 'previous_period') {
                const periodLength = period.period.until.getTime() - period.period.since.getTime();
                startDate = new Date(period.period.since.getTime() - periodLength);
            } else if (comparisonMode.value === 'previous_year') {
                startDate = new Date(period.period.since);
                startDate.setFullYear(startDate.getFullYear() - 1);
            } else {
                startDate = new Date(period.period.since);
            }

            const pageId = currentAnalytic.value?.pageId || 'default';
            const comparisonData = Array.from({ length: daysInRange }, (_, i) => {
                const date = new Date(startDate);
                date.setDate(date.getDate() + i);

                const seed = `${pageId}-${period.alias}-comparison-${comparisonMode.value}-${i}`;
                const random = seededRandom(seed);
                const trendFactor = (i / (daysInRange - 1)) * 0.4 + 0.6;
                const value = Math.floor(random * 15 * trendFactor) + 3;

                return {
                    date: date.toISOString().split('T')[0],
                    value,
                };
            });

            graphData.value = {
                ...graphData.value,
                comparison: comparisonData,
            };
        } catch (err) {
            console.error('Failed to fetch comparison data:', err);
        }
    }

    function setActiveDataKey(key: string): void {
        activeDataKey.value = key;
    }

    const activeItemData = computed(() => {
        if (!activeDataKey.value) return null;
        return items.value.find((item) => item.key === activeDataKey.value) || null;
    });

    async function fetchPageAnalytics(): Promise<void> {
        isLoading.value = true;
        error.value = null;

        try {
            if (pagesStore.pages.length === 0) {
                pagesStore.fetchPages();
                await new Promise((resolve) => setTimeout(resolve, 1100));
            }

            if (analyticList.value.length === 0 || analyticList.value.length !== pagesStore.pages.length) {
                // Sử dụng dữ liệu mẫu cố định thay vì tạo random
                const mockAnalytics: PageAnalytic[] = pagesStore.pages.map((page) => {
                    // Tìm dữ liệu mẫu tương ứng với pageId
                    const existingMockData = mockPageAnalytics.find(mock => mock.pageId === page.id);

                    if (existingMockData) {
                        // Sử dụng dữ liệu mẫu có sẵn
                        return {
                            ...existingMockData,
                            pageData: {
                                title: page.title,
                                status: page.status as 'Published' | 'Unpublished' | 'Draft'
                            }
                        };
                    } else {
                        // Tạo dữ liệu mặc định cho page không có trong mock data
                        return {
                            id: `analytics-${page.id}`,
                            pageId: page.id,
                            pageData: {
                                title: page.title,
                                status: page.status as 'Published' | 'Unpublished' | 'Draft'
                            },
                            status: page.status === 'Published' ? 'Tracking' as const : 'NotTracking' as const,
                            addToCartRate: 0,
                            productViewsRate: 0,
                            visitors: 0,
                            sessions: 0,
                            Sales: 0,
                            Conversion_rate: 0,
                            items: analyticDashboardMock.map(item => ({
                                ...item,
                                value: 0,
                                percent: 0,
                                lastPeriod: 0
                            })),
                            graphData: {
                                sales: []
                            },
                            created_at: new Date().toISOString(),
                            updated_at: new Date().toISOString(),
                        };
                    }
                });

                analyticList.value = mockAnalytics;

                if (!currentAnalytic.value && mockAnalytics.length > 0) {
                    currentAnalytic.value = mockAnalytics[0];
                    // Đảm bảo graphData được set đúng
                    if (mockAnalytics[0].graphData) {
                        graphData.value = mockAnalytics[0].graphData;
                    }
                    console.log('Set currentAnalytic:', mockAnalytics[0]);
                }
            }
        } catch (err) {
            error.value = err instanceof Error ? err.message : 'Failed to fetch page analytics';
        } finally {
            isLoading.value = false;
        }
    }

    function setCurrentAnalytic(analyticId: string): void {
        const analytic = analyticList.value.find((a) => a.id === analyticId);
        if (analytic) {
            currentAnalytic.value = analytic;

            items.value = analytic.items;
            graphData.value = analytic.graphData;

            if (items.value.length > 0) {
                activeDataKey.value = items.value[0].key;
            }
        }
    }

    function updateCurrentAnalyticData(): void {
        if (currentAnalytic.value) {
            const analyticIndex = analyticList.value.findIndex((a) => a.id === currentAnalytic.value!.id);
            if (analyticIndex !== -1) {
                analyticList.value[analyticIndex] = {
                    ...analyticList.value[analyticIndex],
                    items: items.value,
                    graphData: graphData.value,

                    visitors:
                        items.value.find((item) => item.key === 'visitors')?.value ||
                        analyticList.value[analyticIndex].visitors,
                    sessions:
                        items.value.find((item) => item.key === 'sessions')?.value ||
                        analyticList.value[analyticIndex].sessions,
                    addToCartRate:
                        items.value.find((item) => item.key === 'addToCartRate')?.value ||
                        analyticList.value[analyticIndex].addToCartRate,
                    productViewsRate:
                        items.value.find((item) => item.key === 'productViewsRate')?.value ||
                        analyticList.value[analyticIndex].productViewsRate,
                    updated_at: new Date().toISOString(),
                };

                currentAnalytic.value = analyticList.value[analyticIndex];
            }
        }
    }

    const pagesWithAnalytics = computed((): PageWithAnalytics[] => {
        return pagesStore.pages.map((page) => {
            const analytics = analyticList.value.find((a) => a.pageId === page.id);

            if (!analytics) {
                const defaultAnalytics: PageAnalytic = {
                    id: `analytics-${page.id}`,
                    pageId: page.id,
                    pageData: {
                        title: page.title,
                        status: page.status as 'Published' | 'Unpublished' | 'Draft'
                    },
                    status: 'NotTracking',
                    addToCartRate: 0,
                    productViewsRate: 0,
                    visitors: 0,
                    sessions: 0,
                    Sales: 0,
                    Conversion_rate: 0,
                    items: [],
                    graphData: { sales: [] },
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString(),
                };
                return {
                    ...page,
                    analytics: defaultAnalytics,
                };
            }

            let syncedAnalytics = analytics;
            if (currentAnalytic.value && currentAnalytic.value.id === analytics.id) {
                syncedAnalytics = {
                    ...analytics,
                    items: items.value,
                    graphData: graphData.value,

                    visitors: items.value.find((item) => item.key === 'visitors')?.value || analytics.visitors,
                    sessions: items.value.find((item) => item.key === 'sessions')?.value || analytics.sessions,
                    addToCartRate:
                        items.value.find((item) => item.key === 'addToCartRate')?.value || analytics.addToCartRate,
                    productViewsRate:
                        items.value.find((item) => item.key === 'productViewsRate')?.value ||
                        analytics.productViewsRate,
                    Sales: items.value.find((item) => item.key === 'Sales')?.value || analytics.Sales,
                    Conversion_rate: items.value.find((item) => item.key === 'Conversion_rate')?.value || analytics.Conversion_rate,
                };
            }

            return {
                ...page,
                analytics: syncedAnalytics,
            };
        });
    });

    const filteredAnalytics = computed(() => {
        let filtered = [...pagesWithAnalytics.value];

        if (typeFilter.value && typeFilter.value !== 'All') {
            filtered = filtered.filter((pageWithAnalytics) => pageWithAnalytics.type === typeFilter.value);
        }

        if (statusFilter.value.length > 0) {
            if (statusFilter.value.includes('All') && statusFilter.value.length === 1) {
            } else {
                filtered = filtered.filter((pageWithAnalytics) =>
                    statusFilter.value.includes(pageWithAnalytics.status),
                );
            }
        }

        if (queryValue.value) {
            filtered = filtered.filter((pageWithAnalytics) =>
                pageWithAnalytics.title.toLowerCase().includes(queryValue.value.toLowerCase()),
            );
        }

        return filtered;
    });

    const paginatedAnalytics = computed(() => {
        const start = (currentPage.value - 1) * itemsPerPage.value;
        const end = start + itemsPerPage.value;
        return filteredAnalytics.value.slice(start, end);
    });

    const totalPages = computed(() => Math.max(1, Math.ceil(filteredAnalytics.value.length / itemsPerPage.value)));

    const totalAnalytics = computed(() => filteredAnalytics.value.length);

    function setQueryValue(value: string): void {
        queryValue.value = value;
        currentPage.value = 1;
    }

    function setStatusFilter(statuses: string[]): void {
        statusFilter.value = statuses;
        currentPage.value = 1;
    }

    function setTypeFilter(type: string): void {
        typeFilter.value = type;
        currentPage.value = 1;
    }

    function setCurrentPage(page: number): void {
        currentPage.value = page;
    }

    function goToPreviousPage(): void {
        if (currentPage.value > 1) {
            currentPage.value--;
        }
    }

    function goToNextPage(): void {
        if (currentPage.value < totalPages.value) {
            currentPage.value++;
        }
    }

    function updateAnalyticStatus(id: string, newStatus: string): void {
        pagesStore.updatePageStatus(id, newStatus);
    }

    function getAnalyticById(id: string): PageAnalytic | undefined {
        return analyticList.value.find((analytic) => analytic.id === id);
    }

    function loadAnalyticDataInstantly(analyticId: string): boolean {
        const analytic = getAnalyticById(analyticId);
        if (analytic && analytic.items && analytic.items.length > 0) {
            items.value = [...analytic.items];
            graphData.value = { ...analytic.graphData };
            currentAnalytic.value = analytic;

            if (analytic.items.length > 0) {
                activeDataKey.value = analytic.items[0].key;
            }

            return true;
        }
        return false;
    }

    function preloadAnalyticData(pageId: string): void {
        const analytic = getAnalyticByPageId.value(pageId);
        if (analytic && (!analytic.items || analytic.items.length === 0)) {
            const tempCurrentAnalytic = currentAnalytic.value;
            currentAnalytic.value = analytic;
            fetchAnalyticData(currentPeriod.value).then(() => {
                currentAnalytic.value = tempCurrentAnalytic;
            });
        }
    }

    function updateAllAnalyticsForPeriod(_period: DateRange): void {
        if (!analyticList.value || analyticList.value.length === 0) {
            return;
        }

        // Chỉ cập nhật dữ liệu khi thay đổi period, giữ nguyên dữ liệu cố định
        analyticList.value = analyticList.value.map((analytic) => {
            // Tìm dữ liệu mẫu cố định tương ứng
            const mockData = mockPageAnalytics.find(mock => mock.pageId === analytic.pageId);

            if (mockData) {
                // Sử dụng dữ liệu cố định, chỉ cập nhật timestamp
                return {
                    ...mockData,
                    pageData: analytic.pageData, // Giữ nguyên pageData hiện tại
                    updated_at: new Date().toISOString(),
                };
            } else {
                // Giữ nguyên dữ liệu hiện tại cho các page không có mock data
                return {
                    ...analytic,
                    updated_at: new Date().toISOString(),
                };
            }
        });

        if (currentAnalytic.value) {
            const updatedCurrentAnalytic = analyticList.value.find((a) => a.id === currentAnalytic.value!.id);
            if (updatedCurrentAnalytic) {
                currentAnalytic.value = updatedCurrentAnalytic;
                items.value = updatedCurrentAnalytic.items;
                graphData.value = updatedCurrentAnalytic.graphData;
            }
        }
    }

    return {
        items,
        currentPeriod,
        isLoading,
        error,
        comparisonMode,
        graphData,
        activeDataKey,
        analyticList,
        currentAnalytic,

        queryValue,
        statusFilter,
        typeFilter,
        currentPage,
        itemsPerPage,

        getItemByKey,
        getAnalyticByPageId,
        getAnalyticById,
        formattedSalesData,
        formattedComparisonData,
        totalRevenue,
        activeItemData,

        filteredAnalytics,
        paginatedAnalytics,
        totalPages,
        totalAnalytics,

        fetchAnalyticData,
        updateDateRange,
        setComparisonMode,
        fetchComparisonData,
        setActiveDataKey,
        fetchPageAnalytics,
        setCurrentAnalytic,
        updateCurrentAnalyticData,
        loadAnalyticDataInstantly,
        preloadAnalyticData,
        generateConsistentData,
        updateAllAnalyticsForPeriod,

        setQueryValue,
        setStatusFilter,
        setTypeFilter,
        setCurrentPage,
        goToPreviousPage,
        goToNextPage,
        updateAnalyticStatus,
    };
});
