<script setup lang="ts">
import PageAddIcon from '@assets/svgs/page-add-icon.svg';
import { Card, InlineStack, BlockStack, Box, Text } from '@ownego/polaris-vue';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

import { type ColorMap, CardBlockProps } from './types';

const props = defineProps(CardBlockProps);
const { t } = useI18n();

const buttonBackgroundColor = computed(() => {
    if (props.customButtonColor) {
        return props.customButtonColor;
    }

    const colorMap: ColorMap = {
        info: 'var(--p-color-bg-surface-info-hover)',
        success: 'var(--m-primary-base-30)',
        orange: 'var(--m-orange-base-10)',
    };

    return colorMap[props.buttonColor] || colorMap.info;
});

const iconColor = computed(() => {
    const colorMap: { [key: string]: string } = {
        info: 'var(--m-blue-base-10)',
        success: 'var(--m-success-base-10)',
        orange: 'var(--m-orange-base-100)',
    };

    return colorMap[props.buttonColor] || 'var(--m-blue-base-10)';
});

const displayTitle = computed(() => {
    return props.titleKey ? t(props.titleKey) : props.title;
});

const displayDescription = computed(() => {
    return props.descriptionKey ? t(props.descriptionKey) : props.description;
});
</script>

<template>
    <div class="card-block">
        <Card padding="none">
            <div class="card-block__content">
                <InlineStack :gap="'200'">
                    <div class="card-block__icon-container" :style="{ backgroundColor: buttonBackgroundColor }">
                        <PageAddIcon :color="iconColor" class="card-block__icon" />
                    </div>
                    <BlockStack :gap="'050'">
                        <Box>
                            <Text :as="'h2'" :variant="'headingSm'">{{ displayTitle }}</Text>
                        </Box>
                        <Box :maxWidth="'200px'">
                            <div class="card-block__description">
                                {{ displayDescription }}
                            </div>
                        </Box>
                    </BlockStack>
                </InlineStack>
            </div>
        </Card>
    </div>
</template>

<style scoped lang="scss">
.card-block {
    width: 295px;
    cursor: pointer;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;

    &:hover {
        .card-block__content {
            background-color: var(--m-contrast-150);
            z-index: 20;
        }
    }

    @media screen and (max-width: 768px) {
        width: 100%;
        margin-bottom: 16px;
    }

    &__content {
        padding: 16px;
    }

    &__icon-container {
        width: 38px;
        height: 38px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;

        :deep(.Polaris-Box) {
            padding: 9px;
        }
    }

    &__description {
        font-size: 13px;
        line-height: 20px;
        font-weight: 400;
    }
}
</style>
