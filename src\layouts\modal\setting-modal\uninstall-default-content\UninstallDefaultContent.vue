<script setup lang="ts">
import { BlockStack, Box, Button, Text } from '@ownego/polaris-vue';

defineProps<{
    title?: string;
}>();

const emit = defineEmits(['chat']);

const handleChatClick = () => {
    emit('chat');
};
</script>

<template>
    <div>
        <div class="text-container">
            <Box :padding="'500'">
                <BlockStack :gap="'200'">
                    <Text :as="'p'" :variant="'bodyMd'">Don't go yet... </Text>
                    <Text :as="'p'" :variant="'bodyMd'">Is there anything we can help you with? </Text>
                    <Box>
                        <Button variant="primary" @click="handleChatClick">Chat Now </Button>
                    </Box>
                </BlockStack>
            </Box>
        </div>
    </div>
</template>

<style scoped lang="scss">
.text-container {
    padding-block: 30px;
    padding-inline: 20px;
    background-image: url('@/assets/svgs/bearie-cry.svg');
}
</style>
