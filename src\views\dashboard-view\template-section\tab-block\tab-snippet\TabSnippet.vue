<script setup lang="ts">
import { dashboardLocaleVars } from '@locales/dashboard';
import { Box, BlockStack, InlineStack } from '@ownego/polaris-vue';
import { useI18n } from 'vue-i18n';

import { tabs } from '../configs';

const { t } = useI18n();

const props = defineProps<{
    activeTab: string;
}>();

const emit = defineEmits<{
    'tab-change': [tabId: string];
}>();

const onTabChange = (panelID: string) => {
    emit('tab-change', panelID);
};

const getTabClasses = (tabPanelID: string) => {
    return [
        'template__tab-button',
        {
            active: props.activeTab === tabPanelID,
        },
    ];
};

const getTabContent = (content: string) => {
    const normalizedContent = content
        .split(/\s+/)
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
        .join('');
    const key = `tab${normalizedContent}` as keyof typeof dashboardLocaleVars;
    return dashboardLocaleVars[key] ? t(dashboardLocaleVars[key]) : content;
};
</script>

<template>
    <Box>
        <BlockStack gap="800">
            <BlockStack inlineAlign="start">
                <InlineStack>
                    <div v-for="tab in tabs" :key="tab.id" class="template__tab">
                        <div :class="getTabClasses(tab.panelID)" @click="onTabChange(tab.panelID)">
                            <div class="template__tab-button-content">
                                {{ getTabContent(tab.content) }}
                            </div>
                        </div>
                    </div>
                </InlineStack>
            </BlockStack>
        </BlockStack>
    </Box>
</template>

<style scoped lang="scss">
.template__tab {
    margin-right: 10px;

    &-button {
        height: 32px;
        min-height: 32px;
        padding: 8px 12px 8px 12px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        border: 1px solid var(--m-contrast-125);
        transition: all 0.2s ease;
        background-color: transparent;

        &-content {
            height: 16px;
            line-height: 16px;
            font-size: 13px;
            color: inherit;
        }

        &.active {
            background-color: var(--m-black-base);
            border: 1px solid var(--m-black-base);

            .template__tab-button-content {
                color: var(--m-surface-background);
            }
        }
    }

    &:last-child {
        margin-right: 0;
    }
}
</style>
