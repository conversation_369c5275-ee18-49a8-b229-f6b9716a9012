<script lang="ts" setup>
import { Box, Card, Divider, InlineStack, SkeletonDisplayText } from '@ownego/polaris-vue';
</script>

<template>
    <div class="skeleton-overlay">
        <Card :padding="'none'">
            <Box :min-height="'48px'" :padding-inline-start="'400'" :padding-block="'400'">
                <InlineStack>
                    <div class="skeleton-overlay__tab-first">
                        <SkeletonDisplayText />
                    </div>
                    <div v-for="i in 5" :key="i" class="skeleton-overlay__tab">
                        <SkeletonDisplayText />
                    </div>
                </InlineStack>
            </Box>
            <Divider :border-color="'border'" />
            <Box>
                <Box :min-height="'48px'" :padding-inline-start="'400'" :padding-block="'400'">
                    <InlineStack>
                        <div class="skeleton-overlay__tab-first">
                            <SkeletonDisplayText />
                        </div>
                        <div v-for="i in 2" :key="i" class="skeleton-overlay__tab">
                            <SkeletonDisplayText />
                        </div>
                    </InlineStack>
                </Box>
            </Box>
            <Divider :border-color="'border'" />
            <Box>
                <Box :padding-inline-start="'300'" :padding-block="'200'">
                    <InlineStack>
                        <div class="skeleton-overlay__table-heading-first">
                            <SkeletonDisplayText />
                        </div>
                        <div class="skeleton-overlay__table-heading-item-first">
                            <SkeletonDisplayText />
                        </div>
                        <div v-for="i in 3" :key="i" class="skeleton-overlay__table-heading-item">
                            <SkeletonDisplayText />
                        </div>
                    </InlineStack>
                </Box>
            </Box>
            <Divider :border-color="'border'" />
            <Box v-for="i in 3" :key="i">
                <Box :min-height="'48px'" :padding-inline-start="'300'" :padding-block="'150'">
                    <InlineStack>
                        <div class="skeleton-overlay__table-first">
                            <SkeletonDisplayText />
                        </div>
                        <div class="skeleton-overlay__table-item-first">
                            <SkeletonDisplayText class="skeleton-overlay__table-item-first-title" />
                            <SkeletonDisplayText class="skeleton-overlay__table-item-first-subtitle" />
                        </div>
                        <div v-for="i in 3" :key="i" class="skeleton-overlay__table-item">
                            <SkeletonDisplayText />
                        </div>
                        <InlineStack class="skeleton-overlay__table-item-action">
                            <SkeletonDisplayText v-for="i in 3" :key="i" />
                        </InlineStack>
                    </InlineStack>
                </Box>
                <Divider :border-color="'border'" />
            </Box>
            <Box :padding-block="'300'">
                <InlineStack class="skeleton-overlay__paginate" :align="'space-between'">
                    <InlineStack>
                        <SkeletonDisplayText class="skeleton-overlay__paginate-item" />
                    </InlineStack>
                    <div class="skeleton-overlay__paginate-info">
                        <SkeletonDisplayText />
                    </div>
                </InlineStack>
            </Box>
        </Card>
    </div>
</template>

<style scoped lang="scss">
.skeleton-overlay {
    .Polaris-SkeletonDisplayText__DisplayText {
        height: 16px;
    }

    &__tab-first,
    &__table-heading-first,
    &__table-first {
        .Polaris-SkeletonDisplayText__DisplayText {
            width: 16px;
            margin-right: 36px;
        }
    }

    &__tab {
        .Polaris-SkeletonDisplayText__DisplayText {
            width: 60px;
            margin-right: 32px;
        }
    }

    &__table-heading-item-first {
        width: 45%;

        .Polaris-SkeletonDisplayText__DisplayText {
            width: 26px;
            margin-right: 32px;
        }
    }

    &__table-heading-item {
        width: 10%;

        .Polaris-SkeletonDisplayText__DisplayText {
            width: 38px;
        }
    }

    &__table-item-first {
        width: 45%;

        &-title {
            width: 80px;
            height: 16px;
            margin-bottom: 2px;
        }

        &-subtitle {
            width: 60px;
            height: 16px;
        }
    }

    &__table-item {
        width: 10%;
        padding-block: 10px;

        .Polaris-SkeletonDisplayText__DisplayText {
            width: 60px;
        }
    }

    &__table-item-action {
        padding-block: 10px;
        padding-inline-start: 35px;

        .Polaris-SkeletonDisplayText__DisplayText {
            width: 25px;
            margin-right: 19.5px;
        }
    }

    &__paginate {
        padding-inline-start: 12px;

        &-info {
            .Polaris-SkeletonDisplayText__DisplayText {
                width: 100px;
                height: 24px;
                margin-right: 16px;
            }
        }

        &-item {
            width: 100px;
            height: 24px !important;
        }
    }
}
</style>
