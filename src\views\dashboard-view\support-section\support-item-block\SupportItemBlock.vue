<script setup lang="ts">
import { BlockStack, Button, Card, Icon, InlineStack, Text } from '@ownego/polaris-vue';

import { SupportItemBlockProps } from './types';

defineProps(SupportItemBlockProps);
</script>

<template>
    <div class="support-item-block">
        <Card>
            <div class="support-item-block__content">
                <BlockStack :gap="'100'" :blockAlign="'start'">
                    <InlineStack :align="'start'" :gap="'150'">
                        <div class="support-item-block__icon">
                            <Icon :source="icon" />
                        </div>
                        <Text :as="'h2'" :variant="'headingSm'">{{ title }}</Text>
                    </InlineStack>
                    <div class="support-item-block__divider">
                        <Text :as="'p'" :variant="'bodyMd'">
                            {{ description }}
                        </Text>
                    </div>
                </BlockStack>
                <div class="support-item-block__buttons">
                    <Button
                        v-if="primaryButton"
                        :variant="primaryButton.variant || 'secondary'"
                        :url="primaryButton.url"
                        :external="primaryButton.external"
                        @click="primaryButton.onClick"
                    >
                        {{ primaryButton.content }}
                    </Button>
                    <Button
                        v-if="secondaryButton"
                        :variant="secondaryButton.variant || 'secondary'"
                        :url="secondaryButton.url"
                        :external="secondaryButton.external"
                        @click="secondaryButton.onClick"
                    >
                        {{ secondaryButton.content }}
                    </Button>
                </div>
            </div>
        </Card>
    </div>
</template>

<style scoped lang="scss">
.support-item-block {
    width: calc(25% - 12px);
    margin: 0;

    @media (max-width: 991px) {
        width: calc(50% - 8px);
    }

    @media (max-width: 767px) {
        width: 100%;
    }

    &__buttons {
        display: flex;
        gap: 10px;
    }

    &__content {
        display: flex;
        flex-direction: column;
        gap: 10px;
        height: 100%;
        justify-content: space-between;
    }

    &__icon {
        margin: 0;
    }

    &__divider {
        .Polaris-Text--root {
            font-size: 13px;
            font-weight: 400;
            max-height: 40px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
        }
    }
}
</style>
