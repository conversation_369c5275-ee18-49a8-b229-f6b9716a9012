<script setup lang="ts">
import { Box, Divider, InlineStack, Text } from '@ownego/polaris-vue';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

import { pagesLocaleVars } from '@/locales/pages';
import { usePagesStore } from '@/stores/pagesStore';

import { tabs } from '../configs';

const { t } = useI18n();
const pagesStore = usePagesStore();

const props = defineProps<{
    activeTabId: number;
    handleTabClick: (_tabId: number) => void;
}>();

// Map tab IDs to tab content for the store
const tabIdToType = computed(() => {
    const tabMap: Record<number, string> = {};
    tabs.forEach((tab) => {
        tabMap[tab.id] =
            tab.panelID === 'all'
                ? 'All'
                : tab.panelID === 'landing'
                  ? 'Landing'
                  : tab.panelID === 'home'
                    ? 'Home'
                    : tab.panelID === 'product'
                      ? 'Product'
                      : tab.panelID === 'collection'
                        ? 'Collection'
                        : tab.panelID === 'list-collection'
                          ? 'List collection'
                          : '';
    });
    return tabMap;
});

// Handle tab click with store integration
const handleTabClick = (tabId: number) => {
    // Set the tab type in the store
    const tabType = tabIdToType.value[tabId];
    if (tabType) {
        pagesStore.setActiveTab(tabType);
    }
    // Also call the original handler
    props.handleTabClick(tabId);
};

const translatedTabs = computed(() =>
    tabs.map((tab) => ({
        ...tab,
        content:
            tab.panelID === 'all'
                ? t(pagesLocaleVars.tabAll)
                : tab.panelID === 'landing'
                  ? t(pagesLocaleVars.tabLanding)
                  : tab.panelID === 'home'
                    ? t(pagesLocaleVars.tabHome)
                    : tab.panelID === 'product'
                      ? t(pagesLocaleVars.tabProduct)
                      : tab.panelID === 'collection'
                        ? t(pagesLocaleVars.tabCollection)
                        : tab.panelID === 'list-collection'
                          ? t(pagesLocaleVars.tabListCollection)
                          : tab.content,
    })),
);
</script>

<template>
    <Box :padding-inline-start="200" :min-height="'48px'" borderStartEndRadius="0" borderColor="border-neutral-300">
        <InlineStack>
            <div v-for="tab in translatedTabs" :key="tab.id">
                <div
                    class="template__tab"
                    :class="{ active: props.activeTabId === tab.id }"
                    @click="handleTabClick(tab.id)"
                >
                    <Box :padding-inline="100" :padding-block="200">
                        <Box class="inner-box" :padding-inline="300" :padding-block="150">
                            <Text variant="bodyMd" :as="'span'" fontWeight="bold">
                                {{ tab.content }}
                            </Text>
                        </Box>
                    </Box>
                </div>
            </div>
        </InlineStack>
    </Box>
    <Divider borderColor="border" />
</template>

<style scoped lang="scss">
.template__tab {
    position: relative;
    cursor: pointer;

    &:hover {
        .inner-box {
            position: relative;

            &::after {
                content: '';
                position: absolute;
                bottom: -9px;
                left: 0;
                width: 100%;
                height: 2px;
                background-color: var(--m-contrast-125);
                border-top-right-radius: 1px;
                border-top-left-radius: 1px;
            }
        }
    }

    &.active {
        .inner-box {
            position: relative;

            &::after {
                content: '';
                position: absolute;
                bottom: -9px;
                left: 0;
                width: 100%;
                height: 2px;
                background-color: var(--m-success-base-10);
                border-top-right-radius: 8px;
                border-top-left-radius: 8px;
            }
        }
    }

    .inner-box {
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
    }
}
</style>
