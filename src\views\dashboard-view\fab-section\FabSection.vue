<script setup lang="ts">
import { dashboardLocaleVars } from '@locales/dashboard';
import PlusIcon from '@shopify/polaris-icons/PlusIcon.svg';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const handleCreateNewPage = () => {
    const modal = document.getElementById('my-modal') as any;
    if (modal) {
        modal.show();
    }
};
</script>

<template>
    <div class="fab" @click="handleCreateNewPage">
        <div class="fab__icon">
            <PlusIcon />
        </div>
        <span class="fab__label">{{ t(dashboardLocaleVars.miniMenuCreateNewPage) }}</span>
    </div>
</template>

<style scoped lang="scss">
.fab {
    position: fixed;
    right: 6px;
    top: 74%;
    z-index: 100;
    display: flex;
    align-items: center;
    width: 48px;
    height: 48px;
    border-radius: 24px;
    background-color: var(--m-surface-foreground);
    cursor: pointer;
    transition: all 0.3s ease;
    overflow: hidden;

    @media screen and (max-width: 768px) {
        right: 5px;
    }

    &:hover {
        width: 200px;
        border-radius: 24px;

        .fab__label {
            opacity: 1;
            transform: translateX(0);
        }
    }

    &__icon {
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        order: 1;

        svg {
            width: 20px;
            height: 20px;
            fill: var(--m-surface-background);
        }
    }

    &__label {
        color: var(--m-surface-background);
        font-size: 14px;
        font-weight: 500;
        white-space: nowrap;
        opacity: 0;
        transform: translateX(-10px);
        transition: all 0.3s ease;
        padding-left: 8px;
        padding-right: 16px;
        flex: 1;
        order: 2;
    }
}
</style>
