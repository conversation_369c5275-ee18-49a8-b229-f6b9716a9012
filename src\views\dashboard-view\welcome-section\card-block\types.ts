export interface ColorMap {
    info: string;
    success: string;
    orange: string;
    [key: string]: string;
}

export const CardBlockProps = {
    title: {
        type: String,
        required: false,
        default: '',
    },
    titleKey: {
        type: String,
        required: false,
        default: '',
    },
    description: {
        type: String,
        required: false,
        default: '',
    },
    descriptionKey: {
        type: String,
        required: false,
        default: '',
    },
    buttonColor: {
        type: String,
        default: 'info',
        validator: (value: string) => ['info', 'success', 'orange'].includes(value),
    },
    customButtonColor: {
        type: String,
        default: null,
    },
};
