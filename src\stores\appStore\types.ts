export interface AppStoreState {
    isAppLoading: boolean;
    isAuthenticated: boolean;
    token: string | null;
    modal: ModalState;
}

export interface ModalState {
    type: 'default' | 'uninstall-default' | 'uninstall-next';
    isOpen: boolean;
    title: string;
    errorMessage: string;
    successMessage: string;
    isLoading: boolean;
    isSuccess: boolean;
    isError: boolean;
}

export interface AppStoreActions {
    setIsAppLoading: (_isAppLoading: boolean) => void;
    setIsAuthenticated: (_isAuthenticated: boolean) => void;
    setToken: (_token: string) => void;
    setModalState: (_modalState: Partial<ModalState>) => void;
    openModal: (_type: ModalState['type'], _title?: string) => void;
    closeModal: () => void;
}
