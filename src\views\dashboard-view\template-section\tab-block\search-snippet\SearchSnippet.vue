<script setup lang="ts">
import { dashboardLocaleVars } from '@locales/dashboard';
import { Icon, TextField } from '@ownego/polaris-vue';
import SearchIcon from '@shopify/polaris-icons/SearchIcon.svg';
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const emit = defineEmits<{
    'search-change': [query: string];
}>();

const value = ref('');

watch(
    value,
    (newValue) => {
        emit('search-change', newValue);
    },
    { immediate: true },
);

const handleInput = (newValue: string) => {
    value.value = newValue;
};
</script>

<template>
    <div class="template__search">
        <TextField
            :model-value="value"
            @update:model-value="handleInput"
            :autoComplete="'off'"
            :placeholder="t(dashboardLocaleVars.searchTemplatesPlaceholder)"
        >
            <template #prefix>
                <Icon :source="SearchIcon" />
            </template>
        </TextField>
    </div>
</template>

<style scoped lang="scss">
.template__search {
    width: 250px;
    height: 32px;

    :deep(.Polaris-TextField) {
        height: 32px;

        .Polaris-TextField__Input {
            height: 32px;
            min-height: 32px;
        }

        .Polaris-TextField__Prefix {
            height: 32px;
            display: flex;
            align-items: center;
        }
    }
}
</style>
