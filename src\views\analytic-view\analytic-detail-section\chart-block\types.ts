export interface ChartItem {
    key: string;
    label: string;
    value: number;
    percent: number;
    lastPeriod: number;
    unit: string;
}

export interface DateRange {
    alias: string;
    title: string;
    period: {
        since: Date;
        until: Date;
    };
}

export interface DataPoint {
    date: Date;
    value: number;
    originalIndex?: number;
}

export interface ChartData {
    items: ChartItem[];
    graphData: {
        sales: { date: string; value: number }[];
        comparison?: { date: string; value: number }[];
    };
}
