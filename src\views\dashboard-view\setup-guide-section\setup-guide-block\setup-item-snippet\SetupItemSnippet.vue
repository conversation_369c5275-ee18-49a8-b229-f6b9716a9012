<script setup lang="ts">
import OutlineSvg from '@assets/svgs/outline.svg';
import {
    InlineStack,
    Box,
    Text,
    Button,
    ButtonGroup,
    Tooltip,
    Spinner,
    Icon,
    BlockStack,
    Image,
} from '@ownego/polaris-vue';
import CheckIcon from '@shopify/polaris-icons/CheckIcon.svg';
import { ref, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';

import { SetupItemSnippetProps } from './types';

const props = defineProps(SetupItemSnippetProps);

const { t } = useI18n();

const emit = defineEmits(['complete', 'setExpanded']);

const loading = ref(false);
const localComplete = ref(props.complete);

watch(
    () => props.complete,
    (newValue) => {
        localComplete.value = newValue;
    },
    { immediate: true },
);

const completeItem = async () => {
    loading.value = true;

    try {
        emit('complete', props.id);
        await new Promise((resolve) => setTimeout(resolve, 500));
    } catch (error) {
        console.error('Error completing item:', error);
    } finally {
        loading.value = false;
    }
};

const setupItemClasses = computed(() => {
    const classes = ['setup-item'];
    if (props.expanded) {
        classes.push('setup-item--expanded');
    }
    return classes.join(' ');
});

const displayTitle = computed(() => {
    return props.titleKey ? t(props.titleKey) : props.title;
});

const displayDescription = computed(() => {
    return props.descriptionKey ? t(props.descriptionKey) : props.description;
});

const displayPrimaryButtonContent = computed(() => {
    if (!props.primaryButton) return '';
    return props.primaryButton.contentKey ? t(props.primaryButton.contentKey) : props.primaryButton.content;
});

const displaySecondaryButtonContent = computed(() => {
    if (!props.secondaryButton) return '';
    return props.secondaryButton.contentKey ? t(props.secondaryButton.contentKey) : props.secondaryButton.content;
});
</script>

<template>
    <Box borderRadius="200" :background="expanded ? 'bg-surface-active' : undefined">
        <div :class="setupItemClasses">
            <InlineStack :gap="200" :align="'start'" :blockAlign="'start'" :wrap="false">
                <Tooltip :content="localComplete ? 'Mark as done' : 'Mark as not done'" activatorWrapper="div">
                    <Button @click="completeItem" variant="monochromePlain">
                        <div class="setup-item__complete-button">
                            <Spinner v-if="loading" size="small" />
                            <div v-else-if="localComplete" class="setup-item__check-icon">
                                <Icon :source="CheckIcon" />
                            </div>
                            <OutlineSvg v-else />
                        </div>
                    </Button>
                </Tooltip>
                <div
                    class="setup-item__content"
                    @click="expanded ? null : $emit('setExpanded', id)"
                    :style="{
                        cursor: expanded ? 'default' : 'pointer',
                        paddingTop: '.15rem',
                        width: '100%',
                    }"
                >
                    <BlockStack :gap="300" :id="String(id)">
                        <Text :as="'h4'" :variant="expanded ? 'headingSm' : 'bodyMd'">
                            {{ displayTitle }}
                        </Text>
                        <div class="setup-item__accordion" :class="{ 'setup-item__accordion--expanded': expanded }">
                            <div class="setup-item__accordion-content">
                                <Box :paddingBlockEnd="150" :paddingInlineEnd="150">
                                    <BlockStack :gap="400">
                                        <Text :as="'p'" :variant="'bodyMd'">
                                            {{ displayDescription }}
                                        </Text>
                                        <ButtonGroup v-if="primaryButton || secondaryButton" gap="loose">
                                            <Button
                                                v-if="primaryButton"
                                                :variant="'primary'"
                                                v-bind="primaryButton.props || {}"
                                                @click="primaryButton.props?.onClick"
                                            >
                                                {{ displayPrimaryButtonContent }}
                                            </Button>
                                            <Button
                                                v-if="secondaryButton"
                                                :variant="'tertiary'"
                                                v-bind="secondaryButton.props || {}"
                                                @click="secondaryButton.props?.onClick"
                                            >
                                                {{ displaySecondaryButtonContent }}
                                            </Button>
                                        </ButtonGroup>
                                    </BlockStack>
                                </Box>
                            </div>
                        </div>
                    </BlockStack>
                    <Image
                        v-if="image && expanded"
                        class="setup-item__image"
                        :source="image.url || ''"
                        :alt="image.alt || 'image'"
                        style="max-height: 7.75rem"
                    />
                </div>
            </InlineStack>
        </div>
    </Box>
</template>

<style scoped lang="scss">
.setup-item {
    padding: 0.25rem 0.5rem;
    border-radius: 0.5rem;

    &:hover {
        background-color: #f7f7f7;
    }

    &--expanded:hover {
        background-color: inherit;
    }

    &__complete-button {
        width: 1.5rem;
        height: 1.5rem;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #303030;
    }

    &__check-icon {
        width: 1.25rem;
        height: 1.25rem;
        border-radius: 100%;
        background: #303030;
        display: flex;
        justify-content: center;
        align-items: center;
        color: white;
    }

    &__content {
        width: 100%;
        display: flex;
        gap: 8rem;
        justify-content: space-between;
        align-items: flex-start;
    }

    &__image {
        flex-shrink: 0;
        max-width: 200px;
    }

    // Accordion animation styles
    &__accordion {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1);

        &--expanded {
            max-height: 1000px;
        }
    }

    &__accordion-content {
        opacity: 0;
        transform: translateY(-8px);
        transition:
            opacity 0.3s ease 0.1s,
            transform 0.3s ease 0.1s;

        .setup-item__accordion--expanded & {
            opacity: 1;
            transform: translateY(0);
        }
    }
}

@media (min-width: 48em) and (max-width: 61.871875em) {
    .setup-item__image {
        display: none;
    }
}

@media (max-width: 45.625em) {
    .setup-item__image {
        display: none;
    }
}
</style>
