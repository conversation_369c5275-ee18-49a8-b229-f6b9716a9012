const makeDashboardLocaleVars = (text: string) => `DASHBOARD_${text}`;

const dashboardLocaleVars = {
    startBuilding: makeDashboardLocaleVars('START_BUILDING'),
    welcomeTitle: makeDashboardLocaleVars('WELCOME_TITLE'),
    welcomeDescription: makeDashboardLocaleVars('WELCOME_DESCRIPTION'),

    cardCreatePageTitle: makeDashboardLocaleVars('CARD_CREATE_PAGE_TITLE'),
    cardCreateSectionTitle: makeDashboardLocaleVars('CARD_CREATE_SECTION_TITLE'),
    cardCreateBlockTitle: makeDashboardLocaleVars('CARD_CREATE_BLOCK_TITLE'),

    cardCreatePageDesc: makeDashboardLocaleVars('CARD_CREATE_PAGE_DESC'),
    cardCreateSectionDesc: makeDashboardLocaleVars('CARD_CREATE_SECTION_DESC'),
    cardCreateBlockDesc: makeDashboardLocaleVars('CARD_CREATE_BLOCK_DESC'),

    bannerHeading: makeDashboardLocaleVars('BANNER_HEADING'),
    bannerDescription: makeDashboardLocaleVars('BANNER_DESCRIPTION'),
    bannerLearnMore: makeDashboardLocaleVars('BANNER_LEARN_MORE'),
    bannerButtonText: makeDashboardLocaleVars('BANNER_BUTTON_TEXT'),

    sliderTitle: makeDashboardLocaleVars('SLIDER_TITLE'),
    sliderSeeAll: makeDashboardLocaleVars('SLIDER_SEE_ALL'),

    sliderItemTitle: makeDashboardLocaleVars('SLIDER_ITEM_TITLE'),
    sliderItemDescription: makeDashboardLocaleVars('SLIDER_ITEM_DESCRIPTION'),
    sliderItemButtonText: makeDashboardLocaleVars('SLIDER_ITEM_BUTTON_TEXT'),
    sliderItemDismiss: makeDashboardLocaleVars('SLIDER_ITEM_DISMISS'),

    setupGuideShowButton: makeDashboardLocaleVars('SETUP_GUIDE_SHOW_BUTTON'),

    setupGuideTitle: makeDashboardLocaleVars('SETUP_GUIDE_TITLE'),
    setupGuideDescription: makeDashboardLocaleVars('SETUP_GUIDE_DESCRIPTION'),
    setupGuideDone: makeDashboardLocaleVars('SETUP_GUIDE_DONE'),
    setupGuideCompleted: makeDashboardLocaleVars('SETUP_GUIDE_COMPLETED'),
    setupGuideDismiss: makeDashboardLocaleVars('SETUP_GUIDE_DISMISS'),
    setupGuideDismissButton: makeDashboardLocaleVars('SETUP_GUIDE_DISMISS_BUTTON'),

    setupItemAddProduct: makeDashboardLocaleVars('SETUP_ITEM_ADD_PRODUCT'),
    setupItemAddProductDesc: makeDashboardLocaleVars('SETUP_ITEM_ADD_PRODUCT_DESC'),
    setupItemAddProductBtn: makeDashboardLocaleVars('SETUP_ITEM_ADD_PRODUCT_BTN'),
    setupItemImportProductsBtn: makeDashboardLocaleVars('SETUP_ITEM_IMPORT_PRODUCTS_BTN'),
    setupItemShareStore: makeDashboardLocaleVars('SETUP_ITEM_SHARE_STORE'),
    setupItemShareStoreDesc: makeDashboardLocaleVars('SETUP_ITEM_SHARE_STORE_DESC'),
    setupItemCopyLinkBtn: makeDashboardLocaleVars('SETUP_ITEM_COPY_LINK_BTN'),
    setupItemTranslate: makeDashboardLocaleVars('SETUP_ITEM_TRANSLATE'),
    setupItemTranslateDesc: makeDashboardLocaleVars('SETUP_ITEM_TRANSLATE_DESC'),
    setupItemAddLanguageBtn: makeDashboardLocaleVars('SETUP_ITEM_ADD_LANGUAGE_BTN'),

    supportSectionTitle: makeDashboardLocaleVars('SUPPORT_SECTION_TITLE'),

    supportLiveChatTitle: makeDashboardLocaleVars('SUPPORT_LIVE_CHAT_TITLE'),
    supportLiveChatDesc: makeDashboardLocaleVars('SUPPORT_LIVE_CHAT_DESC'),
    supportLiveChatBtn: makeDashboardLocaleVars('SUPPORT_LIVE_CHAT_BTN'),
    supportEmailBtn: makeDashboardLocaleVars('SUPPORT_EMAIL_BTN'),
    supportHelpCenterTitle: makeDashboardLocaleVars('SUPPORT_HELP_CENTER_TITLE'),
    supportHelpCenterDesc: makeDashboardLocaleVars('SUPPORT_HELP_CENTER_DESC'),
    supportHelpCenterBtn: makeDashboardLocaleVars('SUPPORT_HELP_CENTER_BTN'),
    supportCommunityTitle: makeDashboardLocaleVars('SUPPORT_COMMUNITY_TITLE'),
    supportCommunityDesc: makeDashboardLocaleVars('SUPPORT_COMMUNITY_DESC'),
    supportCommunityBtn: makeDashboardLocaleVars('SUPPORT_COMMUNITY_BTN'),
    supportFeatureRequestTitle: makeDashboardLocaleVars('SUPPORT_FEATURE_REQUEST_TITLE'),
    supportFeatureRequestDesc: makeDashboardLocaleVars('SUPPORT_FEATURE_REQUEST_DESC'),
    supportFeatureRequestBtn: makeDashboardLocaleVars('SUPPORT_FEATURE_REQUEST_BTN'),

    miniMenuProfile: makeDashboardLocaleVars('MINI_MENU_PROFILE'),
    miniMenuNotifications: makeDashboardLocaleVars('MINI_MENU_NOTIFICATIONS'),
    miniMenuStore: makeDashboardLocaleVars('MINI_MENU_STORE'),
    miniMenuHelp: makeDashboardLocaleVars('MINI_MENU_HELP'),
    miniMenuLanguage: makeDashboardLocaleVars('MINI_MENU_LANGUAGE'),
    miniMenuCreateNewPage: makeDashboardLocaleVars('MINI_MENU_CREATE_NEW_PAGE'),

    // Template section
    templatesTitle: makeDashboardLocaleVars('TEMPLATES_TITLE'),
    nextButton: makeDashboardLocaleVars('NEXT_BUTTON'),
    searchTemplatesPlaceholder: makeDashboardLocaleVars('SEARCH_TEMPLATES_PLACEHOLDER'),
    noTemplatesFound: makeDashboardLocaleVars('NO_TEMPLATES_FOUND'),
    usedTimes: makeDashboardLocaleVars('USED_TIMES'),
    selected: makeDashboardLocaleVars('SELECTED'),
    select: makeDashboardLocaleVars('SELECT'),

    // Tab names
    tabAll: makeDashboardLocaleVars('TAB_ALL'),
    tabRegular: makeDashboardLocaleVars('TAB_REGULAR'),
    tabHome: makeDashboardLocaleVars('TAB_HOME'),
    tabProduct: makeDashboardLocaleVars('TAB_PRODUCT'),
    tabCollection: makeDashboardLocaleVars('TAB_COLLECTION'),
    tabListCollection: makeDashboardLocaleVars('TAB_LIST_COLLECTION'),

    // Page manager section
    pageManagerTitle: makeDashboardLocaleVars('PAGE_MANAGER_TITLE'),
    pageStatusLabel: makeDashboardLocaleVars('PAGE_STATUS_LABEL'),
    pageStatusAll: makeDashboardLocaleVars('PAGE_STATUS_ALL'),
    pageStatusPublished: makeDashboardLocaleVars('PAGE_STATUS_PUBLISHED'),
    pageStatusUnpublished: makeDashboardLocaleVars('PAGE_STATUS_UNPUBLISHED'),

    pageTableTitle: makeDashboardLocaleVars('PAGE_TABLE_TITLE'),
    pageTableStatus: makeDashboardLocaleVars('PAGE_TABLE_STATUS'),
    pageTableType: makeDashboardLocaleVars('PAGE_TABLE_TYPE'),
    pageTableLastUpdated: makeDashboardLocaleVars('PAGE_TABLE_LAST_UPDATED'),

    pageTypeRegular: makeDashboardLocaleVars('PAGE_TYPE_REGULAR'),
    pageTypeHome: makeDashboardLocaleVars('PAGE_TYPE_HOME'),
    pageTypeProduct: makeDashboardLocaleVars('PAGE_TYPE_PRODUCT'),
    pageTypeCollection: makeDashboardLocaleVars('PAGE_TYPE_COLLECTION'),
    pageTypeListCollection: makeDashboardLocaleVars('PAGE_TYPE_LIST_COLLECTION'),

    pageActionsPublish: makeDashboardLocaleVars('PAGE_ACTIONS_PUBLISH'),
    pageActionsUnpublish: makeDashboardLocaleVars('PAGE_ACTIONS_UNPUBLISH'),
    pageActionsEdit: makeDashboardLocaleVars('PAGE_ACTIONS_EDIT'),
    pageActionsView: makeDashboardLocaleVars('PAGE_ACTIONS_VIEW'),
    pageActionsDelete: makeDashboardLocaleVars('PAGE_ACTIONS_DELETE'),
    pageActionsDuplicate: makeDashboardLocaleVars('PAGE_ACTIONS_DUPLICATE'),
    pageActionsImport: makeDashboardLocaleVars('PAGE_ACTIONS_IMPORT'),
    pageActionsExport: makeDashboardLocaleVars('PAGE_ACTIONS_EXPORT'),

    pageEmptyTitle: makeDashboardLocaleVars('PAGE_EMPTY_TITLE'),
    pageEmptyMessage: makeDashboardLocaleVars('PAGE_EMPTY_MESSAGE'),
    pageEmptyCreateBlank: makeDashboardLocaleVars('PAGE_EMPTY_CREATE_BLANK'),
    pageEmptyCreateTemplate: makeDashboardLocaleVars('PAGE_EMPTY_CREATE_TEMPLATE'),

    pageSearchPlaceholder: makeDashboardLocaleVars('PAGE_SEARCH_PLACEHOLDER'),
};

export default dashboardLocaleVars;
