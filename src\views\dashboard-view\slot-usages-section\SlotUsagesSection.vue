<script setup lang="ts">
import {
    Banner,
    BlockStack,
    Box,
    InlineStack,
    LegacyCard,
    Link,
    ProgressBar,
    Text,
    TextContainer,
} from '@ownego/polaris-vue';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

import { usePlanStore } from '@/stores/planStore';

const { t } = useI18n();
const planStore = usePlanStore();

const formatLimit = (limit: number, isStorage = false) => {
    if (limit === Infinity) return '∞';

    if (isStorage && limit >= 1024) {
        return `${(limit / 1024).toFixed(1)} GB`;
    }

    return limit;
};

const planName = computed(() => {
    return planStore.currentPlan.title;
});

const slotsSectionTitle = computed(() => {
    let baseSlots = planStore.slotLimits.slots;

    if (planStore.currentPlanId === 'pay-as-you-go') {
        const baseAmount = 200;
        const extraSlots = planStore.selectedSlots;
        baseSlots = baseAmount + extraSlots;
    }

    return `Slots (${planStore.totalSlotsUsed} / ${formatLimit(baseSlots)})`;
});

const formsSectionTitle = computed(() => {
    return `Forms & Submissions (${planStore.slotUsage.forms} / ${formatLimit(planStore.slotLimits.forms)} forms, ${planStore.slotUsage.submissions} / ${formatLimit(planStore.slotLimits.submissions)} submissions)`;
});

const storageSectionTitle = computed(() => {
    return `Storage (${planStore.slotUsage.storage} / ${formatLimit(planStore.slotLimits.storage)} MB)`;
});
</script>

<template>
    <LegacyCard>
        <Box :padding="'400'">
            <BlockStack :gap="'200'">
                <Text as="span" variant="headingMd">Slot usages</Text>
                <Text as="span" variant="bodyMd">Current plan: {{ t(planName) }}</Text>
                <Text as="span" variant="bodyMd">{{ slotsSectionTitle }}</Text>
                <ProgressBar :progress="planStore.slotUsagePercentage" :size="'small'" />
                <InlineStack :gap="400">
                    <InlineStack>
                        <div class="slot-usage-item">
                            <span class="slot-usage-item-icon_black"></span>
                            <Text as="span" variant="bodyMd">{{ planStore.slotUsage.pages }}</Text> &nbsp;
                            <Text as="span" variant="bodyMd">Pages</Text>
                        </div>
                    </InlineStack>
                    <InlineStack>
                        <div class="slot-usage-item">
                            <span class="slot-usage-item-icon_gray"></span>
                            <Text as="span" variant="bodyMd">{{ planStore.slotUsage.sections }}</Text> &nbsp;
                            <Text as="span" variant="bodyMd">Sections</Text>
                        </div>
                    </InlineStack>
                </InlineStack>
                <Text as="span" variant="bodyMd">{{ formsSectionTitle }}</Text>
                <ProgressBar :progress="planStore.formAndSubmissionUsagePercentage" :size="'small'" />
                <InlineStack :gap="400">
                    <InlineStack>
                        <div class="slot-usage-item">
                            <span class="slot-usage-item-icon_black"></span>
                            <Text as="span" variant="bodyMd">{{ planStore.slotUsage.forms }}</Text> &nbsp;
                            <Text as="span" variant="bodyMd">Forms</Text>
                        </div>
                    </InlineStack>
                    <InlineStack>
                        <div class="slot-usage-item">
                            <span class="slot-usage-item-icon_gray"></span>
                            <Text as="span" variant="bodyMd">{{ planStore.slotUsage.submissions }}</Text> &nbsp;
                            <Text as="span" variant="bodyMd">Submissions</Text>
                        </div>
                    </InlineStack>
                </InlineStack>
                <Text as="span" variant="bodyMd">{{ storageSectionTitle }}</Text>
                <ProgressBar :progress="planStore.storageUsagePercentage" :size="'small'" />
                <TextContainer>
                    <Banner @dismiss="() => {}">
                        <Text as="p">
                            Use Blum's ready-made sections to launch your store in hours, 5 preset crafted for growth.
                            <Link url="">Check now</Link>
                        </Text>
                    </Banner>
                </TextContainer>
            </BlockStack>
        </Box>
    </LegacyCard>
</template>

<style scoped lang="scss">
.slot-usage-item {
    display: flex;
    align-items: center;
}
.slot-usage-item-icon_black {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 2px;
    margin: 4px;
    background-color: var(--m-black-base);
}
.slot-usage-item-icon_gray {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 2px;
    margin: 4px;
    background-color: rgb(128, 128, 128);
}
</style>
