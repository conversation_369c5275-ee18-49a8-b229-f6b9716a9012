<script setup lang="ts">
import { Page, Badge } from '@ownego/polaris-vue';
import { onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { usePagesStore } from '@/stores';
import { useAnalyticStore } from '@/stores/analyticStore';

import { ChartBlock } from './chart-block';
import { FilterDateBlock } from './filter-date-block';

const analyticStore = useAnalyticStore();
const PageStore = usePagesStore();
const route = useRoute();
const router = useRouter();

const pageId = computed(() => route.params.id as string | undefined);

const PageTitle = computed(() => {
    const page = PageStore.getPageById(pageId.value as string);
    return page;
});

onMounted(async () => {
    const hasAnalyticsList = analyticStore.analyticList.length > 0;

    if (pageId.value) {
        if (hasAnalyticsList) {
            const analytic = analyticStore.getAnalyticByPageId(pageId.value);
            if (analytic) {
                const loadedInstantly = analyticStore.loadAnalyticDataInstantly(analytic.id);

                if (loadedInstantly) {
                    return;
                }
            }
        }

        if (!hasAnalyticsList) {
            await analyticStore.fetchPageAnalytics();
        }

        const analytic = analyticStore.getAnalyticByPageId(pageId.value);
        if (analytic) {
            analyticStore.setCurrentAnalytic(analytic.id);

            await analyticStore.fetchAnalyticData(analyticStore.currentPeriod);

            if (analyticStore.items.length > 0) {
                analyticStore.setActiveDataKey(analyticStore.items[0].key);
            }
        }
    } else {
        if (!hasAnalyticsList) {
            await analyticStore.fetchPageAnalytics();
        }

        if (analyticStore.items.length === 0) {
            await analyticStore.fetchAnalyticData(analyticStore.currentPeriod);
        }

        if (analyticStore.items.length > 0) {
            analyticStore.setActiveDataKey(analyticStore.items[0].key);
        }
    }
});
</script>

<template>
    <Page
        :title="'Analytic Detail'"
        :subtitle="PageTitle?.title"
        :back-action="{
            content: 'Back',
            onAction() {
                router.push('/analytics');
            },
        }"
    >
        <template #pageTitle>
            <span v-if="PageTitle" class="badge-container">
                <Badge
                    class="status-badge"
                    :class="`status-badge--${PageTitle.status.toLowerCase()}`"
                    :progress="PageTitle.status === 'Published' ? 'complete' : 'incomplete'"
                >
                    {{ PageTitle.status }}
                </Badge>
            </span>
        </template>

        <div class="analytic-container">
            <FilterDateBlock />
            <chartBlock />
        </div>
    </Page>
</template>

<style scoped lang="scss">
.analytic-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.status-badge {
    &--published {
        background-color: var(--m-success-base-20);
        color: var(--m-success-base-30);

        svg {
            fill: var(--m-success-base-30);
        }
    }

    &--unpublished {
        background-color: var(--m-overlay-color-2);
    }
}
</style>
