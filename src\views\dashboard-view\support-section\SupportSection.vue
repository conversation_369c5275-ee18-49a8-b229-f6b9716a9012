<script setup lang="ts">
import { dashboardLocaleVars } from '@locales/dashboard';
import { Text } from '@ownego/polaris-vue';
import { useI18n } from 'vue-i18n';

import { supportItems } from './configs';
import { SupportItemBlock } from './support-item-block';

const { t } = useI18n();
</script>

<template>
    <s-section>
        <div class="support-section__header">
            <Text as="h2" variant="headingMd">
                {{ t(dashboardLocaleVars.supportSectionTitle) }}
            </Text>
        </div>
        <div class="support-section__content">
            <SupportItemBlock
                v-for="(item, index) in supportItems"
                :key="index"
                :title="item.titleKey ? t(item.titleKey) : ''"
                :description="item.descriptionKey ? t(item.descriptionKey) : ''"
                :icon="item.icon"
                :primaryButton="
                    item.primaryButton
                        ? {
                              ...item.primaryButton,
                              content: item.primaryButton.contentKey ? t(item.primaryButton.contentKey) : '',
                          }
                        : null
                "
                :secondaryButton="
                    item.secondaryButton
                        ? {
                              ...item.secondaryButton,
                              content: item.secondaryButton.contentKey ? t(item.secondaryButton.contentKey) : '',
                          }
                        : null
                "
            />
        </div>
    </s-section>
</template>

<style scoped lang="scss">
.support-section {
    &__header {
        margin-bottom: 10px;
    }

    &__content {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
    }
}
</style>
