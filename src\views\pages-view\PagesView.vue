<script setup lang="ts">
import { Page } from '@ownego/polaris-vue';
import ExportIcon from '@shopify/polaris-icons/ExportIcon.svg';
import ImportIcon from '@shopify/polaris-icons/ImportIcon.svg';
import { ref } from 'vue';

import { ManagerSection } from './manager-section';
import ModalImport from './manager-section/modal-block/modal-import-snippet/ModalImport.vue';

declare const window: Window & typeof globalThis;
const { document } = window;
const modalImportRef = ref();
const handleImportClick = () => {
    if (modalImportRef.value) {
        modalImportRef.value.showModal();
    }
};
</script>
<template>
    <Page
        title="Pages"
        compactTitle
        :primaryAction="{ content: 'Create page' }"
        :secondaryActions="[
            {
                content: 'Export',
                icon: ExportIcon,
                accessibilityLabel: 'Secondary action label',
                onAction: () => {
                    const modal = document.getElementById('export-page-modal') as HTMLDialogElement;
                    if (modal) {
                        modal.show();
                    }
                },
            },
            {
                content: 'Import',
                icon: ImportIcon,
                onAction: () => handleImportClick(),
            },
        ]"
    >
        <ModalImport ref="modalImportRef" />
        <ManagerSection />
    </Page>
</template>

<style scoped lang="scss"></style>
