export interface FormData {
    form_id: string;
    formtitle: string;
    status: 'Published' | 'Unpublished' | 'Draft';
    submissions_count: number;
    Last_updated: string;
}

export interface SubmissionData {
    id: string;
    form_id: string;
    gmail: string;
    first_name: string;
    last_name: string;
    status: 'Read' | 'Unread';
    Last_updated: string;
}

export interface FilterChoice {
    label: string;
    value: string;
}

export interface SortOption {
    label: string;
    value: string;
    directionLabel: string;
}

export interface Tab {
    content: string;
    index?: number;
    onAction?: () => void;
    id: string | number;
    isLocked?: boolean;
    actions?: any[];
    panelID?: string;
}

export interface AppliedFilter {
    name: string;
    label: string;
    onRemove: () => void;
}
