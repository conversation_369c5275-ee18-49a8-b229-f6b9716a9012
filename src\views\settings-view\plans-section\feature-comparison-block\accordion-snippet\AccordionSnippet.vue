<script setup lang="ts">
import { Box, InlineStack, Text, Collapsible } from '@ownego/polaris-vue';
import ChevronDownIcon from '@shopify/polaris-icons/ChevronDownIcon.svg';
import ChevronUpIcon from '@shopify/polaris-icons/ChevronUpIcon.svg';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';

import { faqItems } from '../../configs';

const { t } = useI18n();
const expanded = ref<number | null>(0);

function toggle(id: number) {
    expanded.value = expanded.value === id ? null : id;
}

const ACCORDION_ITEMS = faqItems.map((item, index) => ({
    id: index,
    title: item.question,
    content: item.answer,
}));
</script>
<template>
    <div v-for="item in ACCORDION_ITEMS" :key="item.id">
        <Box
            borderBlockEndWidth="025"
            borderColor="border"
            background="bg-surface-secondary"
            style="cursor: pointer"
            @click="toggle(item.id)"
        >
            <Box paddingBlock="300" paddingInline="400">
                <div>
                    <InlineStack align="space-between" blockAlign="center">
                        <Text variant="headingMd" as="h1" font-weight="medium">
                            {{ t(item.title) }}
                        </Text>
                        <component
                            :is="expanded === item.id ? ChevronUpIcon : ChevronDownIcon"
                            width="1.5rem"
                            height="1.5rem"
                        />
                    </InlineStack>
                </div>
            </Box>
            <Collapsible
                :open="expanded === item.id"
                :transition="{ duration: '200ms', timingFunction: 'ease-in-out' }"
            >
                <Box padding="400" background="bg-surface">
                    <Text as="span" variant="bodyMd">
                        {{ t(item.content) }}
                    </Text>
                </Box>
            </Collapsible>
        </Box>
    </div>
</template>
