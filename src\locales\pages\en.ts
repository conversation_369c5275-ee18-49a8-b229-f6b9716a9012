import vars from './vars';

export const pagesLocaleEN = {
    [vars.pageManagerTitle]: 'Pages Manager',
    [vars.pageStatusLabel]: 'Status',
    [vars.pageStatusAll]: 'All',
    [vars.pageStatusPublished]: 'Published',
    [vars.pageStatusUnpublished]: 'Unpublished',

    [vars.pageTableTitle]: 'Title',
    [vars.pageTableStatus]: 'Status',
    [vars.pageTableType]: 'Type',
    [vars.pageTableLastUpdated]: 'Last updated',

    [vars.pageTypeRegular]: 'Landing',
    [vars.pageTypeHome]: 'Home',
    [vars.pageTypeProduct]: 'Product',
    [vars.pageTypeCollection]: 'Collection',
    [vars.pageTypeListCollection]: 'List collection',
    [vars.pageTypeLanding]: 'Landing',

    [vars.pageActionsPublish]: 'Publish',
    [vars.pageActionsUnpublish]: 'Unpublish',
    [vars.pageActionsEdit]: 'Edit',
    [vars.pageActionsView]: 'View',
    [vars.pageActionsDelete]: 'Delete',
    [vars.pageActionsDuplicate]: 'Duplicate',
    [vars.pageActionsImport]: 'Import',
    [vars.pageActionsExport]: 'Export',

    [vars.pageEmptyTitle]: 'Build page for your online store',
    [vars.pageEmptyMessage]: 'Customize, settings and more',
    [vars.pageEmptyCreateBlank]: 'Create blank page',
    [vars.pageEmptyCreateTemplate]: 'Create from template',

    [vars.pageSearchPlaceholder]: 'Search pages',

    [vars.tabAll]: 'All',
    [vars.tabLanding]: 'Landing',
    [vars.tabHome]: 'Home',
    [vars.tabProduct]: 'Product',
    [vars.tabCollection]: 'Collection',
    [vars.tabListCollection]: 'List collection',

    [vars.modalDeleteTitle]: 'Delete page',
    [vars.modalDeleteMessage]: 'Are you sure you want to delete this page? This action cannot be undone.',
    [vars.modalDeleteCancel]: 'Cancel',
    [vars.modalDeleteConfirm]: 'Delete',

    [vars.modalDuplicateTitle]: 'Duplicate page',
    [vars.modalDuplicateMessage]: 'Are you sure you want to duplicate this page?',
    [vars.modalDuplicateCancel]: 'Cancel',
    [vars.modalDuplicateConfirm]: 'Duplicate',

    [vars.modalImportTitle]: 'Import page',
    [vars.modalImportMessage]: 'Select a file to import',
    [vars.modalImportCancel]: 'Cancel',
    [vars.modalImportConfirm]: 'Import',
    [vars.modalImportLearnMore]: 'Learn more about',
    [vars.modalImportSupport]: 'Support',
    [vars.modalImportAcceptFileHint]: 'Accepts .matebuilder only',

    [vars.modalExportTitle]: 'Export page',
    [vars.modalExportMessage]: 'Are you sure you want to export this page?',
    [vars.modalExportCancel]: 'Cancel',
    [vars.modalExportConfirm]: 'Export',

    [vars.paginationPrevious]: 'Previous',
    [vars.paginationNext]: 'Next',
    [vars.paginationOf]: 'of',

    [vars.pagesLearnMore]: 'Learn more about',
};
