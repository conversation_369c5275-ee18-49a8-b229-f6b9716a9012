<script setup lang="ts">
import DowloadIcon from '@assets/svgs/DownloadIcon.svg';
import SelectedIcon from '@assets/svgs/selected-icon.svg';
import { dashboardLocaleVars } from '@locales/dashboard';
import { BlockStack, Box, Button, Card, InlineStack, Text } from '@ownego/polaris-vue';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

import type { TemplateItem } from '../configs';

const { t } = useI18n();

const props = defineProps<{
    item: TemplateItem;
}>();
const emit = defineEmits<{
    select: [item: TemplateItem];
    click: [item: TemplateItem];
}>();

const isSelected = computed(() => props.item.selected);
const thumbnailUrl = computed(() => `/src/assets/images/${props.item.thumbnail}`);

const handleSelect = () => {
    emit('select', props.item);
};

const handleClick = () => {
    emit('click', props.item);
};
</script>

<template>
    <div class="list-item-snippet" @click="handleClick">
        <Card padding="300" :class="{ selected: isSelected }">
            <div class="image-container">
                <img :src="thumbnailUrl" :alt="item.name" class="list-item__img" />
            </div>
            <Box>
                <BlockStack gap="150">
                    <Box minHeight="16px">
                        <Text variant="headingMd" as="h6" fontWeight="medium">
                            {{ item.name }}
                        </Text>
                    </Box>
                    <InlineStack :align="'space-between'">
                        <InlineStack gap="100">
                            <DowloadIcon />
                            <Box padding-block-start="100">
                                <div class="list-item__info">
                                    <Text as="p" :variant="'bodyXs'" tone="subdued">
                                        {{ t(dashboardLocaleVars.usedTimes, { count: item.used }) }}
                                    </Text>
                                </div>
                            </Box>
                        </InlineStack>
                        <Box>
                            <Button :variant="isSelected ? 'primary' : 'secondary'" @click.stop="handleSelect">
                                <InlineStack v-if="isSelected" gap="100">
                                    <SelectedIcon /> <Text as="span">{{ t(dashboardLocaleVars.selected) }}</Text>
                                </InlineStack>
                                <Text v-else as="span">{{ t(dashboardLocaleVars.select) }}</Text>
                            </Button>
                        </Box>
                    </InlineStack>
                </BlockStack>
            </Box>
        </Card>
    </div>
</template>

<style scoped lang="scss">
.list-item-snippet {
    width: 100%;
    cursor: pointer;

    .image-container {
        width: 281px;
        height: 281px;
        margin-bottom: 10px;
    }

    .list-item__img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 8px;
    }

    .list-item__info {
        .Polaris-Text--root {
            font-size: 13px;
            font-weight: 400;
        }
    }
}
</style>
