import { computed } from 'vue';

import { usePagesStore } from '@/stores/pagesStore';

import type { AppliedFilter } from './types';

export function useFilters() {
    const pagesStore = usePagesStore();

    const status = computed({
        get: () => pagesStore.status,
        set: (value) => pagesStore.setStatus(value),
    });

    const type = computed({
        get: () => pagesStore.type,
        set: (value) => pagesStore.setType(value),
    });

    const metric = computed({
        get: () => pagesStore.metric,
        set: (value) => pagesStore.setMetric(value),
    });

    const queryValue = computed({
        get: () => pagesStore.queryValue,
        set: (value) => pagesStore.setQueryValue(value),
    });

    const handleStatus = (value: string[]) => {
        pagesStore.setStatus(value);
    };

    const handleType = (value: string[]) => {
        pagesStore.setType(value);
    };

    const handleMetric = (value: string[]) => {
        pagesStore.setMetric(value);
    };

    const handleStatusRemove = () => {
        pagesStore.clearStatus();
    };

    const handleTypeRemove = () => {
        pagesStore.clearType();
    };

    const handleMetricRemove = () => {
        pagesStore.clearMetric();
    };

    const handleQueryValueRemove = () => {
        pagesStore.clearQueryValue();
    };

    const handleFiltersClearAll = () => {
        pagesStore.clearAllFilters();
    };

    const appliedFilters = computed((): AppliedFilter[] => {
        const results: AppliedFilter[] = [];

        if (status.value && !isEmpty(status.value)) {
            const name = 'status';
            results.push({
                name,
                label: disambiguateLabel(name, status.value),
                onRemove: handleStatusRemove,
            });
        }

        if (type.value && !isEmpty(type.value)) {
            const name = 'type';
            results.push({
                name,
                label: disambiguateLabel(name, type.value),
                onRemove: handleTypeRemove,
            });
        }

        if (metric.value && !isEmpty(metric.value)) {
            const name = 'metric';
            results.push({
                name,
                label: disambiguateLabel(name, metric.value),
                onRemove: handleMetricRemove,
            });
        }

        return results;
    });

    function disambiguateLabel(key: string, value: string | any[]): string {
        switch (key) {
            case 'status':
                return (value as string[]).map((val) => val).join(', ');
            case 'type':
                return (value as string[]).map((val) => val).join(', ');
            case 'metric':
                return (value as string[]).map((val) => val).join(', ');
            default:
                return value as string;
        }
    }

    function isEmpty(value: string | string[]): boolean {
        if (Array.isArray(value)) {
            return value.length === 0;
        } else {
            return value === '' || value == null;
        }
    }

    return {
        status,
        type,
        metric,
        queryValue,
        appliedFilters,
        handleStatus,
        handleType,
        handleMetric,
        handleStatusRemove,
        handleTypeRemove,
        handleMetricRemove,
        handleQueryValueRemove,
        handleFiltersClearAll,
    };
}
