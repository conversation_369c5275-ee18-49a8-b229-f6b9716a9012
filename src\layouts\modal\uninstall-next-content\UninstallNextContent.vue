<script setup lang="ts">
import { BlockStack, Box, Checkbox, InlineStack, List, ListItem, Text } from '@ownego/polaris-vue';

defineProps<{
    title?: string;
    isConfirmUninstall: boolean;
}>();

const emit = defineEmits(['confirm']);

const handleConfirm = () => {
    emit('confirm');
};
</script>

<template>
    <div>
        <Box :padding="'500'">
            <BlockStack :gap="'200'">
                <Box>
                    <Text :as="'p'">We're sorry to see you go</Text>
                    <List type="bullet">
                        <ListItem>All PageFly pages and sections</ListItem>
                        <ListItem>All legacy page analytics data</ListItem>
                        <ListItem>All option swatches</ListItem>
                        <ListItem>All uploaded fonts</ListItem>
                        <ListItem>All configured global styles</ListItem>
                        <ListItem>All trashed items</ListItem>
                        <ListItem>All unused Bearie coins</ListItem>
                    </List>
                </Box>
                <Box>
                    <Text :as="'p'">Additionally, you can choose to delete:</Text>
                    <InlineStack>
                        <Checkbox
                            :label="'All published PageFly pages/sections including images, fonts, etc.'"
                            :helpText="'Keep this option unchecked if you still want pages built with PageFly to work on your live store.'"
                        />
                    </InlineStack>
                </Box>
                <Text :as="'p'">
                    Your data will be deleted forever and can’t be recovered. Please consider carefully before
                    uninstalling. Learn more
                </Text>
                <Box>
                    <Checkbox
                        :checked="isConfirmUninstall"
                        label="I understand what I'm doing"
                        @change="handleConfirm"
                    />
                </Box>
            </BlockStack>
        </Box>
    </div>
</template>

<style scoped lang="scss">
.checkbox-container {
    width: 16px;
    height: 16px;
}
</style>
