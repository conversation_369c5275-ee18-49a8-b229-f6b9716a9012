const makePagesLocaleVars = (text: string) => `PAGES_${text}`;

const pagesLocaleVars = {
    pageManagerTitle: makePagesLocaleVars('MANAGER_TITLE'),
    pageStatusLabel: makePagesLocaleVars('STATUS_LABEL'),
    pageStatusAll: makePagesLocaleVars('STATUS_ALL'),
    pageStatusPublished: makePagesLocaleVars('STATUS_PUBLISHED'),
    pageStatusUnpublished: makePagesLocaleVars('STATUS_UNPUBLISHED'),

    pageTableTitle: makePagesLocaleVars('TABLE_TITLE'),
    pageTableStatus: makePagesLocaleVars('TABLE_STATUS'),
    pageTableType: makePagesLocaleVars('TABLE_TYPE'),
    pageTableLastUpdated: makePagesLocaleVars('TABLE_LAST_UPDATED'),

    pageTypeRegular: makePagesLocaleVars('TYPE_REGULAR'),
    pageTypeHome: makePagesLocaleVars('TYPE_HOME'),
    pageTypeProduct: makePagesLocaleVars('TYPE_PRODUCT'),
    pageTypeCollection: makePagesLocaleVars('TYPE_COLLECTION'),
    pageTypeListCollection: makePagesLocaleVars('TYPE_LIST_COLLECTION'),
    pageTypeLanding: makePagesLocaleVars('TYPE_LANDING'),

    pageActionsPublish: makePagesLocaleVars('ACTIONS_PUBLISH'),
    pageActionsUnpublish: makePagesLocaleVars('ACTIONS_UNPUBLISH'),
    pageActionsEdit: makePagesLocaleVars('ACTIONS_EDIT'),
    pageActionsView: makePagesLocaleVars('ACTIONS_VIEW'),
    pageActionsDelete: makePagesLocaleVars('ACTIONS_DELETE'),
    pageActionsDuplicate: makePagesLocaleVars('ACTIONS_DUPLICATE'),
    pageActionsImport: makePagesLocaleVars('ACTIONS_IMPORT'),
    pageActionsExport: makePagesLocaleVars('ACTIONS_EXPORT'),

    pageEmptyTitle: makePagesLocaleVars('EMPTY_TITLE'),
    pageEmptyMessage: makePagesLocaleVars('EMPTY_MESSAGE'),
    pageEmptyCreateBlank: makePagesLocaleVars('EMPTY_CREATE_BLANK'),
    pageEmptyCreateTemplate: makePagesLocaleVars('EMPTY_CREATE_TEMPLATE'),

    pageSearchPlaceholder: makePagesLocaleVars('SEARCH_PLACEHOLDER'),

    tabAll: makePagesLocaleVars('TAB_ALL'),
    tabLanding: makePagesLocaleVars('TAB_LANDING'),
    tabHome: makePagesLocaleVars('TAB_HOME'),
    tabProduct: makePagesLocaleVars('TAB_PRODUCT'),
    tabCollection: makePagesLocaleVars('TAB_COLLECTION'),
    tabListCollection: makePagesLocaleVars('TAB_LIST_COLLECTION'),

    modalDeleteTitle: makePagesLocaleVars('MODAL_DELETE_TITLE'),
    modalDeleteMessage: makePagesLocaleVars('MODAL_DELETE_MESSAGE'),
    modalDeleteCancel: makePagesLocaleVars('MODAL_DELETE_CANCEL'),
    modalDeleteConfirm: makePagesLocaleVars('MODAL_DELETE_CONFIRM'),

    modalDuplicateTitle: makePagesLocaleVars('MODAL_DUPLICATE_TITLE'),
    modalDuplicateMessage: makePagesLocaleVars('MODAL_DUPLICATE_MESSAGE'),
    modalDuplicateCancel: makePagesLocaleVars('MODAL_DUPLICATE_CANCEL'),
    modalDuplicateConfirm: makePagesLocaleVars('MODAL_DUPLICATE_CONFIRM'),

    modalImportTitle: makePagesLocaleVars('MODAL_IMPORT_TITLE'),
    modalImportMessage: makePagesLocaleVars('MODAL_IMPORT_MESSAGE'),
    modalImportCancel: makePagesLocaleVars('MODAL_IMPORT_CANCEL'),
    modalImportConfirm: makePagesLocaleVars('MODAL_IMPORT_CONFIRM'),
    modalImportLearnMore: makePagesLocaleVars('MODAL_IMPORT_LEARN_MORE'),
    modalImportSupport: makePagesLocaleVars('MODAL_IMPORT_SUPPORT'),
    modalImportAcceptFileHint: makePagesLocaleVars('MODAL_IMPORT_ACCEPT_FILE_HINT'),

    modalExportTitle: makePagesLocaleVars('MODAL_EXPORT_TITLE'),
    modalExportMessage: makePagesLocaleVars('MODAL_EXPORT_MESSAGE'),
    modalExportCancel: makePagesLocaleVars('MODAL_EXPORT_CANCEL'),
    modalExportConfirm: makePagesLocaleVars('MODAL_EXPORT_CONFIRM'),

    paginationPrevious: makePagesLocaleVars('PAGINATION_PREVIOUS'),
    paginationNext: makePagesLocaleVars('PAGINATION_NEXT'),
    paginationOf: makePagesLocaleVars('PAGINATION_OF'),

    pagesLearnMore: makePagesLocaleVars('LEARN_MORE'),
};

export default pagesLocaleVars;
