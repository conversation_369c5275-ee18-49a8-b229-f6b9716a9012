import { computed } from 'vue';

import { useAnalyticStore } from '@/stores/analyticStore';

import type { AppliedFilter } from './types';

export function useFilters() {
    const analyticStore = useAnalyticStore();

    const status = computed({
        get: () => analyticStore.statusFilter,
        set: (value) => analyticStore.setStatusFilter(value),
    });

    const type = computed({
        get: () => analyticStore.typeFilter,
        set: (value) => analyticStore.setTypeFilter(value),
    });

    const metric = computed({
        get: () => analyticStore.metricFilter,
        set: (value) => analyticStore.setMetricFilter(value),
    });

    const queryValue = computed({
        get: () => analyticStore.queryValue,
        set: (value) => analyticStore.setQueryValue(value),
    });

    const handleStatus = (value: string[]) => {
        analyticStore.setStatusFilter(value);
    };

    const handleType = (value: string[]) => {
        analyticStore.setTypeFilter(value);
    };

    const handleMetric = (value: string[]) => {
        analyticStore.setMetricFilter(value);
    };

    const handleStatusRemove = () => {
        analyticStore.setStatusFilter([]);
    };

    const handleTypeRemove = () => {
        analyticStore.setTypeFilter([]);
    };

    const handleMetricRemove = () => {
        analyticStore.setMetricFilter([]);
    };

    const handleQueryValueRemove = () => {
        analyticStore.setQueryValue('');
    };

    const handleFiltersClearAll = () => {
        analyticStore.setStatusFilter([]);
        analyticStore.setTypeFilter([]);
        analyticStore.setMetricFilter([]);
        analyticStore.setQueryValue('');
    };

    const appliedFilters = computed((): AppliedFilter[] => {
        const results: AppliedFilter[] = [];

        if (status.value && !isEmpty(status.value)) {
            const name = 'status';
            results.push({
                name,
                label: disambiguateLabel(name, status.value),
                onRemove: handleStatusRemove,
            });
        }

        if (type.value && !isEmpty(type.value)) {
            const name = 'type';
            results.push({
                name,
                label: disambiguateLabel(name, type.value),
                onRemove: handleTypeRemove,
            });
        }

        if (metric.value && !isEmpty(metric.value)) {
            const name = 'metric';
            results.push({
                name,
                label: disambiguateLabel(name, metric.value),
                onRemove: handleMetricRemove,
            });
        }

        return results;
    });

    function disambiguateLabel(key: string, value: string | any[]): string {
        switch (key) {
            case 'status':
                return (value as string[]).map((val) => val).join(', ');
            case 'type':
                return (value as string[]).map((val) => val).join(', ');
            case 'metric':
                return (value as string[]).map((val) => val).join(', ');
            default:
                return value as string;
        }
    }

    function isEmpty(value: string | string[]): boolean {
        if (Array.isArray(value)) {
            return value.length === 0;
        } else {
            return value === '' || value == null;
        }
    }

    return {
        status,
        type,
        metric,
        queryValue,
        appliedFilters,
        handleStatus,
        handleType,
        handleMetric,
        handleStatusRemove,
        handleTypeRemove,
        handleMetricRemove,
        handleQueryValueRemove,
        handleFiltersClearAll,
    };
}
