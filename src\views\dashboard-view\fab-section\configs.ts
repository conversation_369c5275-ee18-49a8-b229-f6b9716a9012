import { dashboardLocaleVars } from '@locales/dashboard';
import NotificationIcon from '@shopify/polaris-icons/NotificationIcon.svg';
import PlanIcon from '@shopify/polaris-icons/PlanIcon.svg';
import HelpIcon from '@shopify/polaris-icons/QuestionCircleIcon.svg';
export interface MenuItem {
    id: string;
    icon: any;
    labelKey: string;
    badge?: number;
    onClick?: () => void;
}

export const menuItems: MenuItem[] = [
    {
        id: 'profile',
        icon: null,
        labelKey: dashboardLocaleVars.miniMenuProfile,
        onClick: () => console.log('Profile clicked'),
    },
    {
        id: 'notifications',
        icon: NotificationIcon,
        labelKey: dashboardLocaleVars.miniMenuNotifications,
        badge: 5,
        onClick: () => console.log('Notifications clicked'),
    },
    {
        id: 'store',
        icon: PlanIcon,
        labelKey: dashboardLocaleVars.miniMenuStore,
        onClick: () => console.log('Store clicked'),
    },
    {
        id: 'help',
        icon: HelpIcon,
        labelKey: dashboardLocaleVars.miniMenuHelp,
        onClick: () => console.log('Help clicked'),
    },
];
