<script setup lang="ts">
import { Tabs } from '@ownego/polaris-vue';
import { ref, computed } from 'vue';

interface Tab {
    id: string;
    content: string;
    isActive?: boolean;
    panelID: string;
}

interface Props {
    tabs: Tab[];
    defaultActiveTab?: number;
}

const props = defineProps<Props>();
const emit = defineEmits(['tabChange']);

const activeTabId = ref(props.defaultActiveTab || 0);

const handleTabClick = (tabId: number) => {
    if (activeTabId.value === tabId) return;
    activeTabId.value = tabId;
    emit('tabChange', tabId);
};

const activeTabs = computed(() => {
    return props.tabs.map((tab) => ({
        ...tab,
        isActive: tab.id === activeTabId.value.toString(),
    }));
});
</script>

<template>
    <header>
        <div>
            <Tabs :tabs="activeTabs" :selected="activeTabId" @select="handleTabClick"> </Tabs>
        </div>
    </header>
</template>

<style scoped lang="scss"></style>
