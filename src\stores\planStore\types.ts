import type { Plan } from '@/views/settings-view/plans-section/types';

export interface SlotLimits {
    slots: number;
    forms: number;
    submissions: number;
    storage: number; // In MB
}

export interface SlotUsage {
    pages: number;
    sections: number;
    forms: number;
    submissions: number;
    storage: number; // In MB
    totalUsed: number;
}

export interface PlanState {
    plans: Plan[];
    currentPlanId: string;
    billingType: 'monthly' | 'annual';
    showFeatureComparison: boolean;
    slotUsage: SlotUsage;
    slotLimits: SlotLimits;
}
