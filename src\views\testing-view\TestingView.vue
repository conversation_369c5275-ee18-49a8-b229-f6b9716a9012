<script lang="ts" setup>
import { BlockStack, MediaCard, Page, VideoThumbnail } from '@ownego/polaris-vue';
</script>

<template>
    <Page title="A/B testing campaigns" :primaryAction="{ content: 'Create new campaign', onAction: () => {} }">
        <BlockStack>
            <MediaCard
                title="A/B Testing"
                description="We prioritize your privacy and security. Our application does not collect sensitive information such as revenue, orders, or customer personal data. The data we gather is linked solely to the Google Analytics account you connect with us, ensuring your information remains protected."
                :primaryAction="{ content: 'Watch Video', onAction: () => {} }"
            >
                <VideoThumbnail
                    thumbnailUrl="https://cdn.shopify.com/s/files/1/0748/2733/3942/files/Ab_testing--Thumbnail.png"
                    :videoLength="80"
                    @click="() => console.log('clicked')"
                />
            </MediaCard>
        </BlockStack>
    </Page>
</template>

<style scoped lang="scss"></style>
